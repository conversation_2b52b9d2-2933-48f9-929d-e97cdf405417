"""
Error Recovery and Fallback System for Yark Tabular Extraction
Provides robust error handling, multiple fallback strategies, and recovery mechanisms
"""

import traceback
import time
from typing import List, Dict, Tuple, Optional, Any, Callable
from enum import Enum
import logging

class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RecoveryStrategy(Enum):
    RETRY_SAME = "retry_same"
    RETRY_DIFFERENT = "retry_different"
    FALLBACK_METHOD = "fallback_method"
    MANUAL_INTERVENTION = "manual_intervention"
    SKIP_AND_CONTINUE = "skip_and_continue"

class ErrorRecoverySystem:
    """Comprehensive error recovery and fallback system"""
    
    def __init__(self):
        self.error_history = []
        self.recovery_attempts = {}
        self.fallback_strategies = {
            'ocr_failure': [
                RecoveryStrategy.RETRY_DIFFERENT,
                RecoveryStrategy.FALLBACK_METHOD,
                RecoveryStrategy.MANUAL_INTERVENTION
            ],
            'table_detection_failure': [
                RecoveryStrategy.RETRY_DIFFERENT,
                RecoveryStrategy.FALLBACK_METHOD,
                RecoveryStrategy.SKIP_AND_CONTINUE
            ],
            'preprocessing_failure': [
                RecoveryStrategy.RETRY_SAME,
                RecoveryStrategy.FALLBACK_METHOD,
                RecoveryStrategy.SKIP_AND_CONTINUE
            ],
            'validation_failure': [
                RecoveryStrategy.RETRY_DIFFERENT,
                RecoveryStrategy.MANUAL_INTERVENTION,
                RecoveryStrategy.SKIP_AND_CONTINUE
            ],
            'file_io_error': [
                RecoveryStrategy.RETRY_SAME,
                RecoveryStrategy.MANUAL_INTERVENTION
            ]
        }
        
        self.max_retry_attempts = 3
        self.retry_delays = [1, 2, 5]  # Exponential backoff
        
        # Configure logging
        self.logger = logging.getLogger('ErrorRecovery')
        self.logger.setLevel(logging.INFO)
    
    def handle_error(self, error: Exception, context: Dict[str, Any], 
                    error_type: str = "general") -> Dict[str, Any]:
        """Main error handling entry point"""
        
        error_info = self.analyze_error(error, context, error_type)
        
        # Log the error
        self.log_error(error_info)
        
        # Determine recovery strategy
        recovery_plan = self.create_recovery_plan(error_info)
        
        # Execute recovery if possible
        recovery_result = self.execute_recovery(recovery_plan, context)
        
        return {
            'error_info': error_info,
            'recovery_plan': recovery_plan,
            'recovery_result': recovery_result,
            'should_continue': recovery_result.get('success', False),
            'fallback_data': recovery_result.get('fallback_data')
        }
    
    def analyze_error(self, error: Exception, context: Dict[str, Any], 
                     error_type: str) -> Dict[str, Any]:
        """Analyze error to determine severity and recovery options"""
        
        error_info = {
            'type': error_type,
            'exception_type': type(error).__name__,
            'message': str(error),
            'traceback': traceback.format_exc(),
            'context': context,
            'timestamp': time.time(),
            'severity': self.determine_error_severity(error, context),
            'is_recoverable': self.is_error_recoverable(error, error_type),
            'previous_attempts': self.get_previous_attempts(context.get('file_path', 'unknown'))
        }
        
        return error_info
    
    def determine_error_severity(self, error: Exception, context: Dict[str, Any]) -> ErrorSeverity:
        """Determine the severity of an error"""
        
        error_type = type(error).__name__
        error_message = str(error).lower()
        
        # Critical errors that stop processing
        if error_type in ['MemoryError', 'SystemExit', 'KeyboardInterrupt']:
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if error_type in ['FileNotFoundError', 'PermissionError', 'OSError']:
            return ErrorSeverity.HIGH
        
        # Medium severity errors
        if any(keyword in error_message for keyword in ['timeout', 'connection', 'network']):
            return ErrorSeverity.MEDIUM
        
        if error_type in ['ValueError', 'TypeError', 'AttributeError']:
            return ErrorSeverity.MEDIUM
        
        # Low severity errors (recoverable)
        return ErrorSeverity.LOW
    
    def is_error_recoverable(self, error: Exception, error_type: str) -> bool:
        """Determine if an error is recoverable"""
        
        # Non-recoverable errors
        non_recoverable = ['MemoryError', 'SystemExit', 'KeyboardInterrupt']
        if type(error).__name__ in non_recoverable:
            return False
        
        # Check if we have recovery strategies for this error type
        return error_type in self.fallback_strategies
    
    def get_previous_attempts(self, file_path: str) -> List[Dict[str, Any]]:
        """Get previous recovery attempts for this file"""
        return [attempt for attempt in self.error_history 
                if attempt.get('context', {}).get('file_path') == file_path]
    
    def create_recovery_plan(self, error_info: Dict[str, Any]) -> Dict[str, Any]:
        """Create a recovery plan based on error analysis"""
        
        if not error_info['is_recoverable']:
            return {
                'strategies': [],
                'can_recover': False,
                'reason': 'Error is not recoverable'
            }
        
        error_type = error_info['type']
        previous_attempts = len(error_info['previous_attempts'])
        
        # Get available strategies for this error type
        available_strategies = self.fallback_strategies.get(error_type, [])
        
        # Filter strategies based on previous attempts
        remaining_strategies = available_strategies[previous_attempts:]
        
        if not remaining_strategies:
            return {
                'strategies': [],
                'can_recover': False,
                'reason': 'All recovery strategies exhausted'
            }
        
        return {
            'strategies': remaining_strategies,
            'can_recover': True,
            'current_strategy': remaining_strategies[0],
            'remaining_attempts': len(remaining_strategies),
            'severity': error_info['severity']
        }
    
    def execute_recovery(self, recovery_plan: Dict[str, Any], 
                        context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the recovery plan"""
        
        if not recovery_plan['can_recover']:
            return {
                'success': False,
                'reason': recovery_plan['reason'],
                'fallback_data': None
            }
        
        strategy = recovery_plan['current_strategy']
        
        try:
            if strategy == RecoveryStrategy.RETRY_SAME:
                return self.retry_same_method(context)
            elif strategy == RecoveryStrategy.RETRY_DIFFERENT:
                return self.retry_different_method(context)
            elif strategy == RecoveryStrategy.FALLBACK_METHOD:
                return self.use_fallback_method(context)
            elif strategy == RecoveryStrategy.MANUAL_INTERVENTION:
                return self.request_manual_intervention(context)
            elif strategy == RecoveryStrategy.SKIP_AND_CONTINUE:
                return self.skip_and_continue(context)
            else:
                return {
                    'success': False,
                    'reason': f'Unknown recovery strategy: {strategy}',
                    'fallback_data': None
                }
                
        except Exception as recovery_error:
            self.logger.error(f"Recovery strategy {strategy} failed: {recovery_error}")
            return {
                'success': False,
                'reason': f'Recovery strategy failed: {recovery_error}',
                'fallback_data': None
            }
    
    def retry_same_method(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Retry the same method with a delay"""
        
        file_path = context.get('file_path', 'unknown')
        attempt_count = len(self.get_previous_attempts(file_path))
        
        if attempt_count >= self.max_retry_attempts:
            return {
                'success': False,
                'reason': 'Maximum retry attempts reached',
                'fallback_data': None
            }
        
        # Apply delay before retry
        delay = self.retry_delays[min(attempt_count, len(self.retry_delays) - 1)]
        print(f"    🔄 Retrying same method after {delay}s delay (attempt {attempt_count + 1})")
        time.sleep(delay)
        
        return {
            'success': True,
            'reason': 'Retry scheduled',
            'action': 'retry_same',
            'delay_applied': delay
        }
    
    def retry_different_method(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Retry with a different method/configuration"""
        
        print("    🔄 Attempting recovery with different method...")
        
        # Suggest alternative methods based on context
        alternatives = self.suggest_alternative_methods(context)
        
        if not alternatives:
            return {
                'success': False,
                'reason': 'No alternative methods available',
                'fallback_data': None
            }
        
        return {
            'success': True,
            'reason': 'Alternative method suggested',
            'action': 'retry_different',
            'alternatives': alternatives
        }
    
    def suggest_alternative_methods(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Suggest alternative processing methods"""
        
        alternatives = []
        error_type = context.get('error_type', 'general')
        
        if error_type == 'ocr_failure':
            alternatives.extend([
                {'method': 'multi_engine_ocr', 'description': 'Try different OCR engine'},
                {'method': 'enhanced_preprocessing', 'description': 'Apply stronger image preprocessing'},
                {'method': 'manual_ocr_config', 'description': 'Use custom OCR configuration'}
            ])
        
        elif error_type == 'table_detection_failure':
            alternatives.extend([
                {'method': 'camelot_extraction', 'description': 'Try Camelot PDF extraction'},
                {'method': 'manual_boundaries', 'description': 'Use manual table boundary detection'},
                {'method': 'alternative_detection', 'description': 'Try different detection algorithm'}
            ])
        
        elif error_type == 'preprocessing_failure':
            alternatives.extend([
                {'method': 'basic_preprocessing', 'description': 'Use basic preprocessing only'},
                {'method': 'no_preprocessing', 'description': 'Skip preprocessing entirely'},
                {'method': 'custom_preprocessing', 'description': 'Apply custom preprocessing profile'}
            ])
        
        return alternatives
    
    def use_fallback_method(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Use a fallback method for processing"""
        
        print("    🛡️ Using fallback processing method...")
        
        fallback_data = self.generate_fallback_data(context)
        
        return {
            'success': True,
            'reason': 'Fallback method applied',
            'action': 'fallback',
            'fallback_data': fallback_data
        }
    
    def generate_fallback_data(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback data when primary processing fails"""
        
        error_type = context.get('error_type', 'general')
        
        if error_type == 'ocr_failure':
            return {
                'table_data': [['Error: OCR processing failed']],
                'confidence': 0.0,
                'method': 'error_fallback',
                'message': 'OCR extraction failed - manual review required'
            }
        
        elif error_type == 'table_detection_failure':
            return {
                'table_data': [['Error: Table detection failed']],
                'confidence': 0.0,
                'method': 'error_fallback',
                'message': 'Table structure could not be detected'
            }
        
        else:
            return {
                'table_data': [['Error: Processing failed']],
                'confidence': 0.0,
                'method': 'error_fallback',
                'message': 'General processing error occurred'
            }
    
    def request_manual_intervention(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Request manual intervention for error resolution"""
        
        print("    👤 Manual intervention required...")
        
        intervention_request = {
            'file_path': context.get('file_path', 'unknown'),
            'error_type': context.get('error_type', 'general'),
            'suggested_actions': self.get_manual_intervention_suggestions(context),
            'priority': 'high' if context.get('severity') == ErrorSeverity.HIGH else 'medium'
        }
        
        return {
            'success': True,
            'reason': 'Manual intervention requested',
            'action': 'manual_intervention',
            'intervention_request': intervention_request
        }
    
    def get_manual_intervention_suggestions(self, context: Dict[str, Any]) -> List[str]:
        """Get suggestions for manual intervention"""
        
        error_type = context.get('error_type', 'general')
        suggestions = []
        
        if error_type == 'ocr_failure':
            suggestions.extend([
                "Check image quality and resolution",
                "Verify table is clearly visible",
                "Consider manual text entry for critical data",
                "Try different image format or scanning settings"
            ])
        
        elif error_type == 'table_detection_failure':
            suggestions.extend([
                "Manually mark table boundaries",
                "Check if image contains a valid table structure",
                "Consider cropping to focus on table area",
                "Verify table has clear borders or grid lines"
            ])
        
        else:
            suggestions.extend([
                "Review error details and context",
                "Check file permissions and accessibility",
                "Verify system resources are available",
                "Consider alternative processing approach"
            ])
        
        return suggestions
    
    def skip_and_continue(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Skip the problematic item and continue processing"""
        
        print("    ⏭️ Skipping problematic item and continuing...")
        
        return {
            'success': True,
            'reason': 'Item skipped',
            'action': 'skip',
            'skipped_item': context.get('file_path', 'unknown')
        }
    
    def log_error(self, error_info: Dict[str, Any]):
        """Log error information for analysis"""
        
        self.error_history.append(error_info)
        
        # Log to console
        severity = error_info['severity']
        error_type = error_info['type']
        message = error_info['message']
        
        print(f"    ❌ {severity.value.upper()} ERROR ({error_type}): {message}")
        
        # Log to file if logger is configured
        if self.logger.handlers:
            self.logger.error(f"Error: {error_type} - {message}", extra=error_info)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics for monitoring"""
        
        if not self.error_history:
            return {'total_errors': 0}
        
        error_types = {}
        severity_counts = {}
        
        for error in self.error_history:
            error_type = error['type']
            severity = error['severity'].value
            
            error_types[error_type] = error_types.get(error_type, 0) + 1
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return {
            'total_errors': len(self.error_history),
            'error_types': error_types,
            'severity_distribution': severity_counts,
            'recent_errors': self.error_history[-5:] if len(self.error_history) > 5 else self.error_history
        }
    
    def clear_error_history(self):
        """Clear error history (useful for testing or reset)"""
        self.error_history.clear()
        self.recovery_attempts.clear()
        print("🗑️ Error history cleared")

# Decorator for automatic error handling
def with_error_recovery(error_type: str = "general"):
    """Decorator to add automatic error recovery to functions"""
    
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Create context from function arguments
                context = {
                    'function_name': func.__name__,
                    'error_type': error_type,
                    'args': str(args)[:100],  # Truncate for logging
                    'kwargs': str(kwargs)[:100]
                }
                
                # Handle error using global recovery system
                recovery_result = error_recovery.handle_error(e, context, error_type)
                
                if recovery_result['should_continue']:
                    # Return fallback data if available
                    return recovery_result.get('fallback_data')
                else:
                    # Re-raise if not recoverable
                    raise e
        
        return wrapper
    return decorator

"""
Batch Processing Optimization System for Yark Tabular Extraction
Handles large-scale processing with memory management, parallel processing, and progress tracking
"""

import os
import gc
import psutil
import threading
import multiprocessing
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from queue import Queue
import time
from typing import List, Dict, Tuple, Optional, Any, Callable
from dataclasses import dataclass
from pathlib import Path

@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing optimization"""
    max_workers: int = 4
    memory_limit_mb: int = 2048
    chunk_size: int = 10
    enable_parallel: bool = True
    progress_callback: Optional[Callable] = None
    error_callback: Optional[Callable] = None
    cleanup_interval: int = 5  # Clean up every N files
    max_retries: int = 2

class BatchProcessingOptimizer:
    """Advanced batch processing system with optimization and monitoring"""

    def __init__(self, config: Optional[BatchProcessingConfig] = None):
        self.config = config or BatchProcessingConfig()
        self.processing_stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'start_time': None,
            'end_time': None,
            'memory_usage': [],
            'processing_times': []
        }
        self.failed_files = []
        self.current_memory_usage = 0
        self.processing_queue = Queue()

    def optimize_batch_processing(self, file_list: List[str],
                                processing_function: Callable,
                                output_dir: str) -> Dict[str, Any]:
        """Main batch processing optimization function"""

        print(f"🚀 Starting optimized batch processing for {len(file_list)} files...")

        # Initialize processing stats
        self.processing_stats['total_files'] = len(file_list)
        self.processing_stats['start_time'] = time.time()

        # Optimize configuration based on system resources
        optimized_config = self.optimize_configuration(len(file_list))

        # Process files in optimized chunks
        if optimized_config.enable_parallel and len(file_list) > 1:
            results = self.process_parallel(file_list, processing_function, output_dir, optimized_config)
        else:
            results = self.process_sequential(file_list, processing_function, output_dir)

        # Finalize stats
        self.processing_stats['end_time'] = time.time()

        # Generate processing report
        report = self.generate_processing_report()

        return {
            'results': results,
            'stats': self.processing_stats,
            'report': report,
            'failed_files': self.failed_files
        }

    def optimize_configuration(self, file_count: int) -> BatchProcessingConfig:
        """Optimize processing configuration based on system resources and file count"""

        # Get system information
        cpu_count = multiprocessing.cpu_count()
        available_memory = psutil.virtual_memory().available // (1024 * 1024)  # MB

        # Optimize worker count
        if file_count < 5:
            max_workers = 1  # Single threaded for small batches
        elif file_count < 20:
            max_workers = min(2, cpu_count // 2)
        else:
            max_workers = min(cpu_count - 1, 8)  # Leave one core free, max 8 workers

        # Optimize memory limit
        memory_limit = min(available_memory // 2, 4096)  # Use half available memory, max 4GB

        # Optimize chunk size
        if file_count < 10:
            chunk_size = file_count
        elif file_count < 100:
            chunk_size = 10
        else:
            chunk_size = max(10, file_count // max_workers)

        optimized_config = BatchProcessingConfig(
            max_workers=max_workers,
            memory_limit_mb=memory_limit,
            chunk_size=chunk_size,
            enable_parallel=file_count > 1 and max_workers > 1
        )

        print(f"  ⚙️ Optimized configuration:")
        print(f"    Workers: {optimized_config.max_workers}")
        print(f"    Memory limit: {optimized_config.memory_limit_mb} MB")
        print(f"    Chunk size: {optimized_config.chunk_size}")
        print(f"    Parallel processing: {optimized_config.enable_parallel}")

        return optimized_config

    def process_parallel(self, file_list: List[str], processing_function: Callable,
                        output_dir: str, config: BatchProcessingConfig) -> List[Dict[str, Any]]:
        """Process files in parallel with memory management"""

        print(f"  🔄 Processing {len(file_list)} files in parallel...")

        results = []
        chunks = self.create_file_chunks(file_list, config.chunk_size)

        with ThreadPoolExecutor(max_workers=config.max_workers) as executor:
            # Submit chunks for processing
            future_to_chunk = {}

            for chunk_idx, chunk in enumerate(chunks):
                future = executor.submit(
                    self.process_chunk_with_monitoring,
                    chunk, processing_function, output_dir, chunk_idx
                )
                future_to_chunk[future] = chunk_idx

            # Collect results as they complete
            for future in as_completed(future_to_chunk):
                chunk_idx = future_to_chunk[future]

                try:
                    chunk_results = future.result()
                    results.extend(chunk_results)

                    # Update progress
                    self.update_progress(len(chunk_results))

                    # Memory cleanup
                    if chunk_idx % config.cleanup_interval == 0:
                        self.cleanup_memory()

                except Exception as e:
                    print(f"    ❌ Chunk {chunk_idx} failed: {e}")
                    if config.error_callback:
                        config.error_callback(f"Chunk {chunk_idx}", e)

        return results

    def process_sequential(self, file_list: List[str], processing_function: Callable,
                          output_dir: str) -> List[Dict[str, Any]]:
        """Process files sequentially with memory management"""

        print(f"  📝 Processing {len(file_list)} files sequentially...")

        results = []

        for idx, file_path in enumerate(file_list):
            try:
                # Monitor memory before processing
                self.monitor_memory_usage()

                # Process single file
                result = self.process_single_file_with_monitoring(
                    file_path, processing_function, output_dir, idx
                )

                if result:
                    results.append(result)
                    self.processing_stats['processed_files'] += 1
                else:
                    self.processing_stats['failed_files'] += 1
                    self.failed_files.append(file_path)

                # Update progress
                self.update_progress(1)

                # Periodic cleanup
                if idx % self.config.cleanup_interval == 0:
                    self.cleanup_memory()

            except Exception as e:
                print(f"    ❌ Failed to process {os.path.basename(file_path)}: {e}")
                self.processing_stats['failed_files'] += 1
                self.failed_files.append(file_path)

                if self.config.error_callback:
                    self.config.error_callback(file_path, e)

        return results

    def create_file_chunks(self, file_list: List[str], chunk_size: int) -> List[List[str]]:
        """Create optimized file chunks for parallel processing"""

        chunks = []
        for i in range(0, len(file_list), chunk_size):
            chunk = file_list[i:i + chunk_size]
            chunks.append(chunk)

        return chunks

    def process_chunk_with_monitoring(self, chunk: List[str], processing_function: Callable,
                                    output_dir: str, chunk_idx: int) -> List[Dict[str, Any]]:
        """Process a chunk of files with monitoring"""

        print(f"    📦 Processing chunk {chunk_idx + 1} ({len(chunk)} files)...")

        chunk_results = []

        for file_path in chunk:
            try:
                result = self.process_single_file_with_monitoring(
                    file_path, processing_function, output_dir, chunk_idx
                )

                if result:
                    chunk_results.append(result)

            except Exception as e:
                print(f"      ❌ Failed in chunk {chunk_idx}: {os.path.basename(file_path)}")
                self.failed_files.append(file_path)

        return chunk_results

    def process_single_file_with_monitoring(self, file_path: str, processing_function: Callable,
                                          output_dir: str, index: int) -> Optional[Dict[str, Any]]:
        """Process a single file with comprehensive monitoring"""

        start_time = time.time()

        try:
            # Generate output path
            file_name = Path(file_path).stem
            output_path = os.path.join(output_dir, f"{file_name}.docx")

            # Call the processing function
            result = processing_function(file_path, output_path)

            # Record processing time
            processing_time = time.time() - start_time
            self.processing_stats['processing_times'].append(processing_time)

            return {
                'file_path': file_path,
                'output_path': output_path,
                'processing_time': processing_time,
                'success': True,
                'result': result
            }

        except Exception as e:
            processing_time = time.time() - start_time

            return {
                'file_path': file_path,
                'processing_time': processing_time,
                'success': False,
                'error': str(e)
            }

    def monitor_memory_usage(self):
        """Monitor current memory usage"""

        process = psutil.Process()
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / (1024 * 1024)

        self.current_memory_usage = memory_mb
        self.processing_stats['memory_usage'].append(memory_mb)

        # Check if memory limit is exceeded
        if memory_mb > self.config.memory_limit_mb:
            print(f"    ⚠️ Memory usage high: {memory_mb:.1f} MB (limit: {self.config.memory_limit_mb} MB)")
            self.cleanup_memory()

    def cleanup_memory(self):
        """Perform memory cleanup"""

        print("    🗑️ Performing memory cleanup...")

        # Force garbage collection
        gc.collect()

        # Additional cleanup for image processing libraries
        try:
            import cv2
            cv2.destroyAllWindows()
        except:
            pass

        # Monitor memory after cleanup
        self.monitor_memory_usage()
        print(f"    📊 Memory after cleanup: {self.current_memory_usage:.1f} MB")

    def update_progress(self, processed_count: int):
        """Update processing progress"""

        self.processing_stats['processed_files'] += processed_count

        total = self.processing_stats['total_files']
        processed = self.processing_stats['processed_files']
        progress = (processed / total) * 100 if total > 0 else 0

        print(f"    📈 Progress: {processed}/{total} ({progress:.1f}%)")

        if self.config.progress_callback:
            self.config.progress_callback(processed, total, progress)

    def generate_processing_report(self) -> Dict[str, Any]:
        """Generate comprehensive processing report"""

        total_time = self.processing_stats['end_time'] - self.processing_stats['start_time']
        processed_files = self.processing_stats['processed_files']
        failed_files = self.processing_stats['failed_files']

        # Calculate statistics
        avg_processing_time = 0
        if self.processing_stats['processing_times']:
            avg_processing_time = sum(self.processing_stats['processing_times']) / len(self.processing_stats['processing_times'])

        avg_memory_usage = 0
        peak_memory_usage = 0
        if self.processing_stats['memory_usage']:
            avg_memory_usage = sum(self.processing_stats['memory_usage']) / len(self.processing_stats['memory_usage'])
            peak_memory_usage = max(self.processing_stats['memory_usage'])

        success_rate = (processed_files / self.processing_stats['total_files']) * 100 if self.processing_stats['total_files'] > 0 else 0

        report = {
            'summary': {
                'total_files': self.processing_stats['total_files'],
                'processed_files': processed_files,
                'failed_files': failed_files,
                'success_rate': success_rate,
                'total_time': total_time,
                'avg_processing_time': avg_processing_time
            },
            'performance': {
                'avg_memory_usage_mb': avg_memory_usage,
                'peak_memory_usage_mb': peak_memory_usage,
                'files_per_second': processed_files / total_time if total_time > 0 else 0
            },
            'configuration': {
                'max_workers': self.config.max_workers,
                'chunk_size': self.config.chunk_size,
                'parallel_enabled': self.config.enable_parallel
            }
        }

        return report

    def print_processing_report(self, report: Dict[str, Any]):
        """Print formatted processing report"""

        print("\n" + "="*60)
        print("📊 BATCH PROCESSING REPORT")
        print("="*60)

        summary = report['summary']
        performance = report['performance']

        print(f"📁 Files processed: {summary['processed_files']}/{summary['total_files']}")
        print(f"✅ Success rate: {summary['success_rate']:.1f}%")
        print(f"⏱️ Total time: {summary['total_time']:.1f} seconds")
        print(f"⚡ Average processing time: {summary['avg_processing_time']:.2f} seconds/file")
        print(f"🚀 Processing speed: {performance['files_per_second']:.2f} files/second")
        print(f"💾 Average memory usage: {performance['avg_memory_usage_mb']:.1f} MB")
        print(f"📈 Peak memory usage: {performance['peak_memory_usage_mb']:.1f} MB")

        if summary['failed_files'] > 0:
            print(f"\n❌ Failed files: {summary['failed_files']}")
            print("Failed file list:")
            for failed_file in self.failed_files[:10]:  # Show first 10 failed files
                print(f"  • {os.path.basename(failed_file)}")
            if len(self.failed_files) > 10:
                print(f"  ... and {len(self.failed_files) - 10} more")

        print("="*60)

# Global instance
batch_optimizer = BatchProcessingOptimizer()

# Global instance
error_recovery = ErrorRecoverySystem()
