# Yark Tabular Extraction System

A comprehensive, AI-powered table extraction system that converts images containing tables into properly formatted Word documents with high accuracy and professional presentation.

## 🚀 Features

### Core Capabilities
- **Multi-Engine OCR**: Advanced OCR with EasyOCR, Tesseract, PaddleOCR, and LaTeX OCR
- **Intelligent Table Detection**: Multiple detection methods with quality assessment
- **Financial Table Processing**: Specialized handling for accounting and financial documents
- **Enhanced Image Preprocessing**: Adaptive image optimization for better OCR results
- **Professional Word Output**: Beautifully formatted Word documents with proper styling

### Advanced Features
- **Error Recovery System**: Comprehensive error handling with automatic fallback strategies
- **Performance Monitoring**: Real-time analytics and performance optimization
- **Batch Processing**: Optimized parallel processing for large document sets
- **Quality Assessment**: Automatic quality scoring and improvement recommendations
- **Layout Analysis**: Advanced table structure analysis and optimization
- **Comprehensive Testing**: Automated testing suite with regression testing

## 📋 Requirements

### System Requirements
- Python 3.8 or higher
- Windows 10/11 (primary support)
- 4GB RAM minimum (8GB recommended for large batches)
- 2GB free disk space

### Python Dependencies
```bash
pip install -r requirements.txt
```

### Optional Dependencies
For enhanced functionality:
```bash
# For Camelot PDF table extraction
pip install camelot-py[cv]

# For advanced mathematical OCR
pip install latex-ocr

# For enhanced image processing
pip install opencv-python-headless
```

## 🛠️ Installation

1. **Clone or download the repository**
   ```bash
   git clone <repository-url>
   cd yark-tabular-extraction
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**
   ```bash
   python run_tests.py --test-type unit
   ```

## 🎯 Quick Start

### GUI Application
```bash
python yark_tabular_extraction_gui.py
```

### Command Line
```bash
python main.py
```

### Batch Processing
```bash
python main.py --input-dir /path/to/images --output-dir /path/to/output
```

## 📖 Usage Guide

### Basic Usage

1. **Launch the GUI application**
   - Run `python yark_tabular_extraction_gui.py`
   - Select input folder containing table images
   - Select output folder for Word documents
   - Click "Start Processing"

2. **Supported Image Formats**
   - PNG, JPG, JPEG, TIF, BMP
   - Recommended: High-resolution images (300+ DPI)
   - Clear table borders and text

### Advanced Configuration

#### OCR Engine Selection
The system automatically selects the best OCR engine based on content type:
- **EasyOCR**: General text recognition
- **Tesseract**: Fast processing for clear text
- **PaddleOCR**: Advanced structured data
- **LaTeX OCR**: Mathematical expressions

#### Image Preprocessing Profiles
- **Financial Tables**: Enhanced contrast and line detection
- **Mathematical Content**: Optimized for equations and symbols
- **Low Quality**: Aggressive noise reduction and enhancement
- **High Quality**: Minimal processing to preserve quality

#### Performance Optimization
- **Parallel Processing**: Automatic for batches >5 files
- **Memory Management**: Automatic cleanup and monitoring
- **Quality Assessment**: Real-time feedback and recommendations

## 🏗️ Architecture

### System Components

1. **Enhanced Table Structure Detection** (`enhanced_table_structure_detection.py`)
   - Multi-method detection with line-based, contour-based, and text region analysis
   - Intelligent cell filtering and grid organization
   - Advanced cell merging for complex layouts

2. **Multi-Engine OCR System** (`multi_engine_ocr.py`)
   - Intelligent engine selection based on content analysis
   - Automatic fallback mechanisms
   - Performance monitoring and adaptive learning

3. **Financial Table Processor** (`financial_table_processor.py`)
   - Specialized financial table type detection
   - Currency formatting and accounting abbreviation expansion
   - Financial validation and totals calculation

4. **Advanced Image Preprocessor** (`advanced_image_preprocessor.py`)
   - Comprehensive image quality analysis
   - Adaptive preprocessing profiles
   - Skew correction and noise reduction

5. **Intelligent Post-Processor** (`intelligent_postprocessor.py`)
   - Context-aware text validation and correction
   - OCR error pattern recognition
   - Table consistency validation

6. **Enhanced Camelot Integration** (`enhanced_camelot_integration.py`)
   - Robust PDF table extraction
   - Multiple extraction methods with quality assessment
   - Comprehensive error handling

7. **Quality Assessment System** (`quality_assessment_system.py`)
   - Multi-dimensional quality scoring
   - Automatic retry recommendations
   - Confidence scoring and validation

8. **Advanced Layout Analyzer** (`advanced_table_layout_analyzer.py`)
   - Complex table structure analysis
   - Header detection and column type inference
   - Spanning cell detection and hierarchy analysis

9. **Error Recovery System** (`error_recovery_system.py`)
   - Comprehensive error handling with multiple fallback strategies
   - Automatic retry mechanisms
   - Performance monitoring integration

10. **Enhanced Word Output** (`enhanced_word_output.py`)
    - Professional document formatting
    - Mathematical equation support
    - Context-aware styling

11. **Performance Monitoring** (`performance_monitoring_system.py`)
    - Real-time performance analytics
    - Bottleneck identification
    - Comprehensive reporting

12. **Testing Suite** (`comprehensive_testing_suite.py`)
    - Automated unit and integration testing
    - Performance benchmarking
    - Regression testing

### Processing Pipeline

```
Image Input → Preprocessing → Table Detection → OCR Processing → 
Post-Processing → Layout Analysis → Document Generation → Output
```

Each stage includes:
- Error recovery mechanisms
- Performance monitoring
- Quality assessment
- Automatic optimization

## 🧪 Testing

### Run All Tests
```bash
python run_tests.py --test-type all
```

### Generate Test Data
```bash
python run_tests.py --generate-data --test-data-dir test_data
```

### Performance Benchmarks
```bash
python run_tests.py --test-type performance
```

### Regression Testing
```bash
python run_tests.py --test-type regression --baseline-file baselines.json
```

## 📊 Performance Optimization

### Batch Processing Tips
- Use parallel processing for batches >5 files
- Monitor memory usage for large images
- Enable performance monitoring for optimization insights

### Image Quality Guidelines
- Minimum 300 DPI for best results
- Clear table borders improve detection accuracy
- Avoid skewed or rotated images when possible
- Ensure good contrast between text and background

### System Optimization
- Allocate sufficient RAM (8GB+ for large batches)
- Use SSD storage for faster I/O
- Close unnecessary applications during processing

## 🔧 Configuration

### Environment Variables
```bash
# Optional: Set custom temp directory
export YARK_TEMP_DIR=/path/to/temp

# Optional: Set memory limit (MB)
export YARK_MEMORY_LIMIT=4096

# Optional: Set max workers for parallel processing
export YARK_MAX_WORKERS=4
```

### Configuration Files
- `config.json`: System configuration
- `ocr_config.json`: OCR engine settings
- `preprocessing_profiles.json`: Image preprocessing profiles

## 📈 Monitoring and Analytics

### Real-time Monitoring
- Processing speed (files/minute)
- Memory usage tracking
- Success rate monitoring
- Quality score analysis

### Performance Reports
- Comprehensive analytics export
- Bottleneck identification
- Method effectiveness analysis
- Error pattern analysis

### Quality Metrics
- Overall accuracy score
- Text extraction confidence
- Table structure quality
- Processing consistency

## 🛠️ Troubleshooting

### Common Issues

1. **Low OCR Accuracy**
   - Check image quality and resolution
   - Verify table borders are clear
   - Try different preprocessing profiles

2. **Memory Issues**
   - Reduce batch size
   - Close other applications
   - Increase system RAM

3. **Slow Processing**
   - Enable parallel processing
   - Use performance monitoring to identify bottlenecks
   - Optimize image preprocessing

4. **Table Detection Failures**
   - Ensure clear table structure
   - Try manual boundary marking
   - Check for skewed images

### Error Recovery
The system includes automatic error recovery:
- Automatic retry with different methods
- Fallback to alternative processing
- Graceful degradation with partial results

## 📝 API Reference

### Main Functions

#### `process_image_to_docx(image_path, output_path)`
Process a single image to Word document.

**Parameters:**
- `image_path` (str): Path to input image
- `output_path` (str): Path for output Word document

**Returns:**
- `bool`: Success status

#### `batch_process_images(input_dir, output_dir, config=None)`
Process multiple images in batch.

**Parameters:**
- `input_dir` (str): Directory containing images
- `output_dir` (str): Directory for output documents
- `config` (dict, optional): Processing configuration

**Returns:**
- `dict`: Batch processing results

### Configuration Classes

#### `BatchProcessingConfig`
Configuration for batch processing optimization.

#### `ProcessingMetrics`
Performance metrics for individual file processing.

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Install development dependencies: `pip install -r requirements-dev.txt`
4. Run tests: `python run_tests.py`
5. Submit a pull request

### Code Style
- Follow PEP 8 guidelines
- Use type hints where possible
- Include comprehensive docstrings
- Add unit tests for new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- EasyOCR team for excellent OCR capabilities
- Tesseract OCR project
- PaddleOCR developers
- Camelot team for PDF table extraction
- OpenCV community for image processing tools

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting guide
- Review the comprehensive test suite for examples

---

**Yark Tabular Extraction System** - Transforming table images into professional documents with AI-powered precision.
