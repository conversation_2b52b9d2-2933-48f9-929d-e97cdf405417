"""
Advanced Table Layout Analysis for Yark Tabular Extraction
Handles complex table structures, multi-column layouts, header detection, and spanning cells
"""

import re
import numpy as np
from typing import List, Dict, Tuple, Optional, Any, Set
from collections import defaultdict
import statistics

class AdvancedTableLayoutAnalyzer:
    """Advanced table layout analyzer for complex table structures"""
    
    def __init__(self):
        self.header_indicators = [
            'total', 'subtotal', 'grand total', 'summary', 'balance',
            'assets', 'liabilities', 'equity', 'revenue', 'expenses',
            'description', 'account', 'amount', 'date', 'reference',
            'debit', 'credit', 'dr', 'cr', 'particulars'
        ]
        
        self.footer_indicators = [
            'total', 'grand total', 'sum', 'balance', 'net',
            'carried forward', 'brought forward', 'c/f', 'b/f'
        ]
        
        self.spanning_indicators = [
            'continued', 'subtotal', 'total', 'summary',
            'assets', 'liabilities', 'current', 'non-current'
        ]
    
    def analyze_table_layout(self, table_data: List[List[str]], 
                           cell_positions: Optional[List[List[Dict]]] = None) -> Dict[str, Any]:
        """Comprehensive table layout analysis"""
        
        if not table_data:
            return {'layout_type': 'empty', 'structure': {}}
        
        print("  📐 Starting advanced table layout analysis...")
        
        # Basic structure analysis
        structure_info = self.analyze_basic_structure(table_data)
        
        # Header detection and analysis
        header_info = self.detect_and_analyze_headers(table_data)
        
        # Column type analysis
        column_analysis = self.analyze_column_types(table_data)
        
        # Spanning cell detection
        spanning_analysis = self.detect_spanning_cells(table_data, cell_positions)
        
        # Multi-level structure detection
        hierarchy_info = self.detect_hierarchical_structure(table_data)
        
        # Layout pattern recognition
        layout_pattern = self.recognize_layout_pattern(table_data, structure_info, header_info)
        
        # Generate layout recommendations
        recommendations = self.generate_layout_recommendations(
            structure_info, header_info, column_analysis, spanning_analysis
        )
        
        layout_analysis = {
            'layout_type': layout_pattern,
            'structure': structure_info,
            'headers': header_info,
            'columns': column_analysis,
            'spanning_cells': spanning_analysis,
            'hierarchy': hierarchy_info,
            'recommendations': recommendations,
            'complexity_score': self.calculate_complexity_score(
                structure_info, header_info, spanning_analysis, hierarchy_info
            )
        }
        
        print(f"    📊 Layout type: {layout_pattern}")
        print(f"    📈 Complexity score: {layout_analysis['complexity_score']:.2f}")
        print(f"    🏗️ Structure: {structure_info['rows']}×{structure_info['max_columns']} table")
        
        return layout_analysis
    
    def analyze_basic_structure(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Analyze basic table structure"""
        rows = len(table_data)
        column_counts = [len(row) for row in table_data]
        max_columns = max(column_counts) if column_counts else 0
        min_columns = min(column_counts) if column_counts else 0
        
        # Calculate column consistency
        column_consistency = 1.0 - (max_columns - min_columns) / max_columns if max_columns > 0 else 1.0
        
        # Detect empty rows and columns
        empty_rows = [i for i, row in enumerate(table_data) 
                     if all(not cell.strip() for cell in row)]
        
        empty_columns = []
        for col_idx in range(max_columns):
            column_data = [row[col_idx] if col_idx < len(row) else "" for row in table_data]
            if all(not cell.strip() for cell in column_data):
                empty_columns.append(col_idx)
        
        return {
            'rows': rows,
            'max_columns': max_columns,
            'min_columns': min_columns,
            'column_consistency': column_consistency,
            'empty_rows': empty_rows,
            'empty_columns': empty_columns,
            'density': self.calculate_data_density(table_data)
        }
    
    def calculate_data_density(self, table_data: List[List[str]]) -> float:
        """Calculate the density of non-empty cells"""
        total_cells = sum(len(row) for row in table_data)
        filled_cells = sum(1 for row in table_data for cell in row if cell and cell.strip())
        return filled_cells / total_cells if total_cells > 0 else 0.0
    
    def detect_and_analyze_headers(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Detect and analyze table headers"""
        if not table_data:
            return {'header_rows': [], 'header_type': 'none'}
        
        header_candidates = []
        
        # Check first few rows for header characteristics
        for row_idx in range(min(3, len(table_data))):
            row = table_data[row_idx]
            header_score = self.calculate_header_score(row, row_idx, table_data)
            
            if header_score > 0.5:
                header_candidates.append({
                    'row_index': row_idx,
                    'score': header_score,
                    'content': row
                })
        
        # Determine header type and structure
        if not header_candidates:
            header_type = 'none'
            header_rows = []
        elif len(header_candidates) == 1:
            header_type = 'single'
            header_rows = [header_candidates[0]['row_index']]
        else:
            header_type = 'multi_level'
            header_rows = [h['row_index'] for h in header_candidates]
        
        # Analyze header content
        header_analysis = self.analyze_header_content(header_candidates, table_data)
        
        return {
            'header_rows': header_rows,
            'header_type': header_type,
            'candidates': header_candidates,
            'analysis': header_analysis
        }
    
    def calculate_header_score(self, row: List[str], row_idx: int, table_data: List[List[str]]) -> float:
        """Calculate likelihood that a row is a header"""
        score = 0.0
        
        # Position bias (earlier rows more likely to be headers)
        position_score = max(0, 1.0 - row_idx * 0.3)
        score += position_score * 0.3
        
        # Content analysis
        row_text = ' '.join(row).lower()
        
        # Check for header keywords
        header_keyword_count = sum(1 for indicator in self.header_indicators 
                                 if indicator in row_text)
        if header_keyword_count > 0:
            score += min(0.4, header_keyword_count * 0.2)
        
        # Check for formatting patterns typical of headers
        if any(cell.isupper() for cell in row if cell.strip()):
            score += 0.2
        
        # Check for lack of numeric data (headers usually don't have numbers)
        numeric_cells = sum(1 for cell in row if self.contains_numbers(cell))
        if numeric_cells == 0 and any(cell.strip() for cell in row):
            score += 0.2
        elif numeric_cells / len(row) < 0.3:
            score += 0.1
        
        # Check consistency with data rows
        if row_idx < len(table_data) - 1:
            next_row = table_data[row_idx + 1]
            if self.is_data_row(next_row):
                score += 0.2
        
        return min(1.0, score)
    
    def contains_numbers(self, text: str) -> bool:
        """Check if text contains numeric data"""
        return bool(re.search(r'\d', text))
    
    def is_data_row(self, row: List[str]) -> bool:
        """Check if row appears to contain data rather than headers"""
        numeric_cells = sum(1 for cell in row if self.contains_numbers(cell))
        return numeric_cells > 0 or any(len(cell.strip()) > 20 for cell in row)
    
    def analyze_header_content(self, header_candidates: List[Dict], table_data: List[List[str]]) -> Dict[str, Any]:
        """Analyze header content for column mapping"""
        if not header_candidates:
            return {'column_labels': [], 'column_types': []}
        
        # Use the best header candidate
        best_header = max(header_candidates, key=lambda h: h['score'])
        header_row = best_header['content']
        
        column_labels = [cell.strip() for cell in header_row]
        column_types = []
        
        # Infer column types from headers and data
        for col_idx, label in enumerate(column_labels):
            col_type = self.infer_column_type_from_header(label, col_idx, table_data)
            column_types.append(col_type)
        
        return {
            'column_labels': column_labels,
            'column_types': column_types,
            'best_header_row': best_header['row_index']
        }
    
    def infer_column_type_from_header(self, label: str, col_idx: int, table_data: List[List[str]]) -> str:
        """Infer column data type from header label and content"""
        label_lower = label.lower()
        
        # Check header text for type indicators
        if any(indicator in label_lower for indicator in ['amount', 'total', 'balance', 'value', '$', '£', '€']):
            return 'currency'
        elif any(indicator in label_lower for indicator in ['date', 'time', 'period']):
            return 'date'
        elif any(indicator in label_lower for indicator in ['percent', '%', 'rate']):
            return 'percentage'
        elif any(indicator in label_lower for indicator in ['quantity', 'count', 'number', 'qty']):
            return 'numeric'
        elif any(indicator in label_lower for indicator in ['description', 'name', 'account', 'particulars']):
            return 'text'
        
        # Analyze column data to infer type
        column_data = [row[col_idx] if col_idx < len(row) else "" 
                      for row in table_data[1:] if len(table_data) > 1]  # Skip header
        
        if column_data:
            return self.infer_column_type_from_data(column_data)
        
        return 'unknown'
    
    def infer_column_type_from_data(self, column_data: List[str]) -> str:
        """Infer column type from actual data"""
        non_empty_data = [cell for cell in column_data if cell and cell.strip()]
        
        if not non_empty_data:
            return 'empty'
        
        # Count different data patterns
        currency_count = sum(1 for cell in non_empty_data if re.search(r'[\$£€¥₹]', cell))
        date_count = sum(1 for cell in non_empty_data if re.search(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', cell))
        percentage_count = sum(1 for cell in non_empty_data if re.search(r'\d+\.?\d*\s*%', cell))
        numeric_count = sum(1 for cell in non_empty_data if re.search(r'^\d+\.?\d*$', cell.replace(',', '')))
        
        total_cells = len(non_empty_data)
        
        # Determine type based on majority pattern
        if currency_count / total_cells > 0.5:
            return 'currency'
        elif date_count / total_cells > 0.5:
            return 'date'
        elif percentage_count / total_cells > 0.5:
            return 'percentage'
        elif numeric_count / total_cells > 0.5:
            return 'numeric'
        else:
            return 'text'
    
    def analyze_column_types(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Analyze column types and characteristics"""
        if not table_data:
            return {'column_info': []}
        
        max_cols = max(len(row) for row in table_data)
        column_info = []
        
        for col_idx in range(max_cols):
            column_data = [row[col_idx] if col_idx < len(row) else "" for row in table_data]
            
            col_analysis = {
                'index': col_idx,
                'type': self.infer_column_type_from_data(column_data[1:]),  # Skip header
                'width_stats': self.analyze_column_width(column_data),
                'content_stats': self.analyze_column_content(column_data),
                'alignment_hint': self.suggest_column_alignment(column_data)
            }
            
            column_info.append(col_analysis)
        
        return {'column_info': column_info}
    
    def analyze_column_width(self, column_data: List[str]) -> Dict[str, Any]:
        """Analyze column width characteristics"""
        lengths = [len(cell) for cell in column_data if cell]
        
        if not lengths:
            return {'min': 0, 'max': 0, 'avg': 0, 'std': 0}
        
        return {
            'min': min(lengths),
            'max': max(lengths),
            'avg': statistics.mean(lengths),
            'std': statistics.stdev(lengths) if len(lengths) > 1 else 0
        }
    
    def analyze_column_content(self, column_data: List[str]) -> Dict[str, Any]:
        """Analyze column content characteristics"""
        non_empty = [cell for cell in column_data if cell and cell.strip()]
        
        return {
            'fill_rate': len(non_empty) / len(column_data) if column_data else 0,
            'unique_values': len(set(non_empty)),
            'has_duplicates': len(non_empty) != len(set(non_empty)),
            'avg_word_count': statistics.mean([len(cell.split()) for cell in non_empty]) if non_empty else 0
        }
    
    def suggest_column_alignment(self, column_data: List[str]) -> str:
        """Suggest text alignment for column based on content"""
        non_empty = [cell for cell in column_data if cell and cell.strip()]
        
        if not non_empty:
            return 'left'
        
        # Check for numeric content
        numeric_count = sum(1 for cell in non_empty if re.search(r'^\d+\.?\d*$', cell.replace(',', '')))
        currency_count = sum(1 for cell in non_empty if re.search(r'[\$£€¥₹]', cell))
        
        if (numeric_count + currency_count) / len(non_empty) > 0.7:
            return 'right'  # Numbers typically right-aligned
        
        # Check for short labels (might be center-aligned)
        avg_length = statistics.mean([len(cell) for cell in non_empty])
        if avg_length < 10 and len(set(non_empty)) < len(non_empty) * 0.8:
            return 'center'
        
        return 'left'  # Default for text content
    
    def detect_spanning_cells(self, table_data: List[List[str]], 
                            cell_positions: Optional[List[List[Dict]]] = None) -> Dict[str, Any]:
        """Detect cells that span multiple columns or rows"""
        spanning_cells = []
        
        # If we don't have position data, use content-based detection
        if not cell_positions:
            spanning_cells = self.detect_spanning_by_content(table_data)
        else:
            spanning_cells = self.detect_spanning_by_position(table_data, cell_positions)
        
        return {
            'spanning_cells': spanning_cells,
            'has_spanning': len(spanning_cells) > 0,
            'spanning_patterns': self.analyze_spanning_patterns(spanning_cells)
        }
    
    def detect_spanning_by_content(self, table_data: List[List[str]]) -> List[Dict[str, Any]]:
        """Detect spanning cells based on content analysis"""
        spanning_cells = []
        
        for row_idx, row in enumerate(table_data):
            for col_idx, cell in enumerate(row):
                if not cell or not cell.strip():
                    continue
                
                # Check for spanning indicators in content
                cell_lower = cell.lower()
                if any(indicator in cell_lower for indicator in self.spanning_indicators):
                    # Check if this cell is significantly longer than others in the row
                    other_cells = [c for i, c in enumerate(row) if i != col_idx and c.strip()]
                    if other_cells:
                        avg_length = statistics.mean([len(c) for c in other_cells])
                        if len(cell) > avg_length * 1.5:
                            spanning_cells.append({
                                'row': row_idx,
                                'col': col_idx,
                                'content': cell,
                                'type': 'content_based',
                                'confidence': 0.7
                            })
        
        return spanning_cells
    
    def detect_spanning_by_position(self, table_data: List[List[str]], 
                                  cell_positions: List[List[Dict]]) -> List[Dict[str, Any]]:
        """Detect spanning cells based on position data"""
        # This would require actual cell position data from table detection
        # For now, return empty list as position data is not typically available
        return []
    
    def analyze_spanning_patterns(self, spanning_cells: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze patterns in spanning cells"""
        if not spanning_cells:
            return {'patterns': [], 'common_types': []}
        
        # Group by row to find patterns
        row_groups = defaultdict(list)
        for cell in spanning_cells:
            row_groups[cell['row']].append(cell)
        
        patterns = []
        if len(row_groups) > 1:
            patterns.append('multi_row_spanning')
        
        # Analyze content types
        content_types = [cell.get('type', 'unknown') for cell in spanning_cells]
        common_types = list(set(content_types))
        
        return {
            'patterns': patterns,
            'common_types': common_types,
            'row_distribution': dict(row_groups)
        }
    
    def detect_hierarchical_structure(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Detect hierarchical structure in table data"""
        hierarchy_info = {
            'has_hierarchy': False,
            'levels': [],
            'indentation_pattern': None,
            'grouping_structure': []
        }
        
        # Look for indentation patterns in first column
        if not table_data or not table_data[0]:
            return hierarchy_info
        
        first_column = [row[0] if row else "" for row in table_data]
        indentation_levels = self.detect_indentation_levels(first_column)
        
        if len(set(indentation_levels)) > 1:
            hierarchy_info['has_hierarchy'] = True
            hierarchy_info['levels'] = sorted(set(indentation_levels))
            hierarchy_info['indentation_pattern'] = indentation_levels
        
        # Detect grouping by content
        grouping_structure = self.detect_content_grouping(table_data)
        hierarchy_info['grouping_structure'] = grouping_structure
        
        return hierarchy_info
    
    def detect_indentation_levels(self, column_data: List[str]) -> List[int]:
        """Detect indentation levels in text"""
        levels = []
        
        for text in column_data:
            if not text:
                levels.append(0)
                continue
            
            # Count leading spaces
            leading_spaces = len(text) - len(text.lstrip())
            # Convert to indentation level (assuming 2-4 spaces per level)
            level = leading_spaces // 2
            levels.append(level)
        
        return levels
    
    def detect_content_grouping(self, table_data: List[List[str]]) -> List[Dict[str, Any]]:
        """Detect content-based grouping"""
        groups = []
        
        # Look for subtotal/total rows that indicate grouping
        for row_idx, row in enumerate(table_data):
            row_text = ' '.join(row).lower()
            
            if any(indicator in row_text for indicator in ['subtotal', 'total', 'summary']):
                groups.append({
                    'type': 'summary_row',
                    'row_index': row_idx,
                    'content': row_text
                })
        
        return groups
    
    def recognize_layout_pattern(self, table_data: List[List[str]], 
                               structure_info: Dict, header_info: Dict) -> str:
        """Recognize overall table layout pattern"""
        
        # Simple table
        if (structure_info['column_consistency'] > 0.9 and 
            header_info['header_type'] in ['single', 'none'] and
            structure_info['rows'] < 20):
            return 'simple_table'
        
        # Financial statement
        if any(indicator in ' '.join([' '.join(row) for row in table_data]).lower() 
               for indicator in ['assets', 'liabilities', 'equity', 'balance']):
            return 'financial_statement'
        
        # Multi-level header table
        if header_info['header_type'] == 'multi_level':
            return 'multi_level_header'
        
        # Complex table with spanning
        if structure_info['column_consistency'] < 0.8:
            return 'complex_irregular'
        
        # Large data table
        if structure_info['rows'] > 50:
            return 'large_data_table'
        
        return 'standard_table'
    
    def calculate_complexity_score(self, structure_info: Dict, header_info: Dict, 
                                 spanning_analysis: Dict, hierarchy_info: Dict) -> float:
        """Calculate table complexity score (0-1)"""
        complexity = 0.0
        
        # Structure complexity
        if structure_info['column_consistency'] < 0.9:
            complexity += 0.2
        
        if len(structure_info['empty_rows']) > 0:
            complexity += 0.1
        
        # Header complexity
        if header_info['header_type'] == 'multi_level':
            complexity += 0.3
        elif header_info['header_type'] == 'single':
            complexity += 0.1
        
        # Spanning cells
        if spanning_analysis['has_spanning']:
            complexity += 0.2
        
        # Hierarchical structure
        if hierarchy_info['has_hierarchy']:
            complexity += 0.2
        
        return min(1.0, complexity)
    
    def generate_layout_recommendations(self, structure_info: Dict, header_info: Dict, 
                                      column_analysis: Dict, spanning_analysis: Dict) -> List[str]:
        """Generate recommendations for table processing"""
        recommendations = []
        
        # Structure recommendations
        if structure_info['column_consistency'] < 0.8:
            recommendations.append("Consider manual column alignment for irregular structure")
        
        if structure_info['density'] < 0.5:
            recommendations.append("Table has low data density - verify extraction completeness")
        
        # Header recommendations
        if header_info['header_type'] == 'none':
            recommendations.append("No clear headers detected - consider manual header identification")
        elif header_info['header_type'] == 'multi_level':
            recommendations.append("Multi-level headers detected - use specialized processing")
        
        # Column recommendations
        column_info = column_analysis.get('column_info', [])
        numeric_columns = sum(1 for col in column_info if col['type'] in ['numeric', 'currency'])
        if numeric_columns > len(column_info) * 0.7:
            recommendations.append("Primarily numeric data - ensure proper number formatting")
        
        # Spanning cell recommendations
        if spanning_analysis['has_spanning']:
            recommendations.append("Spanning cells detected - may need manual cell merging")
        
        return recommendations

# Global instance
layout_analyzer = AdvancedTableLayoutAnalyzer()
