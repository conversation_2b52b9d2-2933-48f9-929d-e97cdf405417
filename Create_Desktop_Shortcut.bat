@echo off
echo Creating desktop shortcut for Yark Tabular Extraction...

set "SCRIPT_DIR=%~dp0"
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT_NAME=Yark Tabular Extraction.lnk"

powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\%SHORTCUT_NAME%'); $Shortcut.TargetPath = '%SCRIPT_DIR%run_yark_tabular_extraction.bat'; $Shortcut.WorkingDirectory = '%SCRIPT_DIR%'; $Shortcut.IconLocation = '%SCRIPT_DIR%logo and icon\Icon.ico'; $Shortcut.Description = 'Yark Tabular Extraction - Advanced OCR Table Processing'; $Shortcut.Save()}"

if exist "%DESKTOP%\%SHORTCUT_NAME%" (
    echo ✅ Desktop shortcut created successfully!
    echo You can now double-click "Yark Tabular Extraction" on your desktop to launch the application.
) else (
    echo ❌ Failed to create desktop shortcut.
)

pause
