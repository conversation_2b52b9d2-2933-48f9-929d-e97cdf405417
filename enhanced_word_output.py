"""
Enhanced Word Document Output System for Yark Tabular Extraction
Provides professional document formatting, mathematical equation handling, and advanced styling
"""

import os
import re
from typing import List, Dict, Tuple, Optional, Any
from docx import Document
from docx.shared import Inches, RGBColor, Pt
from docx.enum.table import WD_TABLE_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls
from docx.enum.style import WD_STYLE_TYPE
from docx.table import _Cell
import datetime

class EnhancedWordDocumentGenerator:
    """Advanced Word document generator with professional formatting and equation support"""
    
    def __init__(self):
        self.document_styles = {
            'financial_statement': {
                'title_color': RGBColor(0, 51, 102),  # Dark blue
                'header_color': RGBColor(68, 114, 196),  # Blue
                'border_color': RGBColor(68, 114, 196),
                'font_name': 'Calibri',
                'title_size': 16,
                'header_size': 12,
                'body_size': 11
            },
            'mathematical': {
                'title_color': RGBColor(102, 51, 0),  # Brown
                'header_color': RGBColor(153, 102, 51),  # Light brown
                'border_color': RGBColor(153, 102, 51),
                'font_name': 'Times New Roman',
                'title_size': 16,
                'header_size': 12,
                'body_size': 11
            },
            'general': {
                'title_color': RGBColor(51, 51, 51),  # Dark gray
                'header_color': RGBColor(102, 102, 102),  # Gray
                'border_color': RGBColor(102, 102, 102),
                'font_name': 'Calibri',
                'title_size': 14,
                'header_size': 11,
                'body_size': 10
            }
        }
        
        self.equation_patterns = [
            r'[=+\-×÷∑∏∫]',  # Mathematical operators
            r'\b(sin|cos|tan|log|ln|sqrt)\b',  # Functions
            r'\^\d+|\b\d+\^\d+',  # Exponents
            r'\b\d+/\d+\b',  # Fractions
            r'[()]\s*\d+',  # Parentheses with numbers
        ]
    
    def create_enhanced_document(self, table_data: List[List[str]], 
                               metadata: Dict[str, Any]) -> Document:
        """Create an enhanced Word document with professional formatting"""
        
        print("  📄 Creating enhanced Word document...")
        
        # Create document
        doc = Document()
        
        # Determine document style based on content
        document_type = self.detect_document_type(table_data, metadata)
        style_config = self.document_styles.get(document_type, self.document_styles['general'])
        
        # Apply document-wide styles
        self.apply_document_styles(doc, style_config)
        
        # Add title and metadata
        self.add_document_header(doc, metadata, style_config)
        
        # Add processing information
        self.add_processing_info(doc, metadata, style_config)
        
        # Create enhanced table
        if table_data:
            self.create_enhanced_table(doc, table_data, document_type, style_config)
        else:
            self.add_no_data_message(doc, style_config)
        
        # Add footer
        self.add_document_footer(doc, metadata, style_config)
        
        print(f"    ✅ Enhanced document created ({document_type} style)")
        
        return doc
    
    def detect_document_type(self, table_data: List[List[str]], 
                           metadata: Dict[str, Any]) -> str:
        """Detect document type for appropriate styling"""
        
        if not table_data:
            return 'general'
        
        # Combine all text for analysis
        all_text = ' '.join([' '.join(row) for row in table_data]).lower()
        
        # Check for financial content
        financial_indicators = ['assets', 'liabilities', 'equity', 'capital', 'revenue', 
                              'profit', 'loss', 'balance', 'sheet', 'statement']
        if any(indicator in all_text for indicator in financial_indicators):
            return 'financial_statement'
        
        # Check for mathematical content
        math_indicators = ['equation', 'formula', 'solution', '=', '+', '-', '×', '÷']
        if any(indicator in all_text for indicator in math_indicators):
            return 'mathematical'
        
        return 'general'
    
    def apply_document_styles(self, doc: Document, style_config: Dict[str, Any]):
        """Apply document-wide styles"""
        
        # Create custom styles
        styles = doc.styles
        
        # Title style
        try:
            title_style = styles.add_style('CustomTitle', WD_STYLE_TYPE.PARAGRAPH)
            title_font = title_style.font
            title_font.name = style_config['font_name']
            title_font.size = Pt(style_config['title_size'])
            title_font.color.rgb = style_config['title_color']
            title_font.bold = True
            title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
            title_style.paragraph_format.space_after = Pt(12)
        except:
            pass  # Style might already exist
        
        # Header style
        try:
            header_style = styles.add_style('CustomHeader', WD_STYLE_TYPE.PARAGRAPH)
            header_font = header_style.font
            header_font.name = style_config['font_name']
            header_font.size = Pt(style_config['header_size'])
            header_font.color.rgb = style_config['header_color']
            header_font.bold = True
            header_style.paragraph_format.space_after = Pt(6)
        except:
            pass
        
        # Body style
        try:
            body_style = styles.add_style('CustomBody', WD_STYLE_TYPE.PARAGRAPH)
            body_font = body_style.font
            body_font.name = style_config['font_name']
            body_font.size = Pt(style_config['body_size'])
            body_style.paragraph_format.space_after = Pt(3)
        except:
            pass
    
    def add_document_header(self, doc: Document, metadata: Dict[str, Any], 
                          style_config: Dict[str, Any]):
        """Add professional document header"""
        
        # Main title
        file_name = metadata.get('file_name', 'Unknown File')
        title = f'Table Extraction Report: {file_name}'
        
        title_para = doc.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.font.name = style_config['font_name']
        title_run.font.size = Pt(style_config['title_size'])
        title_run.font.color.rgb = style_config['title_color']
        title_run.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add separator line
        doc.add_paragraph('_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add spacing
        doc.add_paragraph()
    
    def add_processing_info(self, doc: Document, metadata: Dict[str, Any], 
                          style_config: Dict[str, Any]):
        """Add processing information section"""
        
        # Processing info header
        info_header = doc.add_paragraph()
        info_run = info_header.add_run('Processing Information')
        info_run.font.name = style_config['font_name']
        info_run.font.size = Pt(style_config['header_size'])
        info_run.font.color.rgb = style_config['header_color']
        info_run.bold = True
        
        # Processing details
        details = [
            f"Processed: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Image size: {metadata.get('image_size', 'Unknown')}",
            f"Processing method: {metadata.get('processing_method', 'Advanced OCR')}",
            f"Quality score: {metadata.get('quality_score', 'N/A')}",
            f"Table type: {metadata.get('table_type', 'General')}"
        ]
        
        for detail in details:
            para = doc.add_paragraph()
            para.add_run('• ').font.bold = True
            para.add_run(detail)
            para.style = doc.styles.get('CustomBody', doc.styles['Normal'])
        
        # Add spacing
        doc.add_paragraph()
    
    def create_enhanced_table(self, doc: Document, table_data: List[List[str]], 
                            document_type: str, style_config: Dict[str, Any]):
        """Create enhanced table with professional formatting"""
        
        if not table_data:
            return
        
        # Table header
        table_header = doc.add_paragraph()
        header_run = table_header.add_run('Extracted Table Data')
        header_run.font.name = style_config['font_name']
        header_run.font.size = Pt(style_config['header_size'])
        header_run.font.color.rgb = style_config['header_color']
        header_run.bold = True
        
        # Determine table dimensions
        max_cols = max(len(row) for row in table_data)
        
        # Ensure consistent column count
        normalized_data = []
        for row in table_data:
            normalized_row = row + [''] * (max_cols - len(row))
            normalized_data.append(normalized_row)
        
        # Create table
        table = doc.add_table(rows=len(normalized_data), cols=max_cols)
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Apply table styling
        self.apply_table_styling(table, style_config)
        
        # Fill table data
        for row_idx, row_data in enumerate(normalized_data):
            for col_idx, cell_data in enumerate(row_data):
                cell = table.cell(row_idx, col_idx)
                
                # Process cell content
                self.format_cell_content(cell, cell_data, row_idx, col_idx, 
                                       document_type, style_config)
        
        # Apply header formatting
        if table.rows:
            self.format_table_header(table.rows[0], style_config)
        
        # Add table caption
        caption = doc.add_paragraph()
        caption_run = caption.add_run(f'Table: Extracted data ({len(normalized_data)} rows × {max_cols} columns)')
        caption_run.font.size = Pt(9)
        caption_run.italic = True
        caption.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        doc.add_paragraph()  # Add spacing
    
    def apply_table_styling(self, table, style_config: Dict[str, Any]):
        """Apply professional styling to table"""
        
        # Set table style
        table.style = 'Table Grid'
        
        # Apply borders
        for row in table.rows:
            for cell in row.cells:
                # Set cell margins
                cell.vertical_alignment = WD_ALIGN_VERTICAL.CENTER
                
                # Apply border styling
                tc = cell._tc
                tcPr = tc.get_or_add_tcPr()
                
                # Add borders
                borders = parse_xml(f'''
                    <w:tcBorders {nsdecls('w')}>
                        <w:top w:val="single" w:sz="4" w:space="0" w:color="{style_config['border_color'].rgb:06x}"/>
                        <w:left w:val="single" w:sz="4" w:space="0" w:color="{style_config['border_color'].rgb:06x}"/>
                        <w:bottom w:val="single" w:sz="4" w:space="0" w:color="{style_config['border_color'].rgb:06x}"/>
                        <w:right w:val="single" w:sz="4" w:space="0" w:color="{style_config['border_color'].rgb:06x}"/>
                    </w:tcBorders>
                ''')
                tcPr.append(borders)
    
    def format_cell_content(self, cell: _Cell, content: str, row_idx: int, col_idx: int,
                          document_type: str, style_config: Dict[str, Any]):
        """Format individual cell content with appropriate styling"""
        
        if not content:
            return
        
        # Clear existing content
        cell.text = ''
        
        # Check if content contains mathematical expressions
        if self.is_mathematical_content(content):
            self.add_mathematical_content(cell, content, style_config)
        else:
            self.add_regular_content(cell, content, row_idx, style_config)
    
    def is_mathematical_content(self, text: str) -> bool:
        """Check if text contains mathematical expressions"""
        
        if not text:
            return False
        
        # Check for mathematical patterns
        for pattern in self.equation_patterns:
            if re.search(pattern, text):
                return True
        
        return False
    
    def add_mathematical_content(self, cell: _Cell, content: str, 
                               style_config: Dict[str, Any]):
        """Add mathematical content with proper formatting"""
        
        paragraph = cell.paragraphs[0]
        run = paragraph.add_run(content)
        
        # Apply mathematical formatting
        run.font.name = 'Times New Roman'  # Better for math
        run.font.size = Pt(style_config['body_size'])
        
        # Center align mathematical content
        paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def add_regular_content(self, cell: _Cell, content: str, row_idx: int,
                          style_config: Dict[str, Any]):
        """Add regular content with appropriate alignment"""
        
        paragraph = cell.paragraphs[0]
        run = paragraph.add_run(content)
        
        # Apply regular formatting
        run.font.name = style_config['font_name']
        run.font.size = Pt(style_config['body_size'])
        
        # Determine alignment based on content
        if self.is_numeric_content(content):
            paragraph.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        elif row_idx == 0:  # Header row
            paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
        else:
            paragraph.alignment = WD_ALIGN_PARAGRAPH.LEFT
    
    def is_numeric_content(self, text: str) -> bool:
        """Check if content is primarily numeric"""
        
        if not text:
            return False
        
        # Remove common formatting characters
        clean_text = text.replace(',', '').replace('$', '').replace('(', '').replace(')', '').strip()
        
        # Check if it's a number
        try:
            float(clean_text)
            return True
        except ValueError:
            pass
        
        # Check for currency or percentage
        if re.search(r'[\$£€¥₹]\s*[\d,]+\.?\d*', text) or re.search(r'\d+\.?\d*\s*%', text):
            return True
        
        return False
    
    def format_table_header(self, header_row, style_config: Dict[str, Any]):
        """Format table header row"""
        
        for cell in header_row.cells:
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.font.bold = True
                    run.font.color.rgb = style_config['header_color']
                
                # Center align headers
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # Add header background (light gray)
            cell._tc.get_or_add_tcPr().append(
                parse_xml(f'<w:shd {nsdecls("w")} w:fill="F2F2F2"/>')
            )
    
    def add_no_data_message(self, doc: Document, style_config: Dict[str, Any]):
        """Add message when no table data is available"""
        
        message_para = doc.add_paragraph()
        message_run = message_para.add_run('No table data could be extracted from the image.')
        message_run.font.name = style_config['font_name']
        message_run.font.size = Pt(style_config['body_size'])
        message_run.italic = True
        message_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add suggestions
        doc.add_paragraph()
        suggestions_header = doc.add_paragraph()
        suggestions_run = suggestions_header.add_run('Suggestions:')
        suggestions_run.font.bold = True
        
        suggestions = [
            "Ensure the image contains a clear table structure",
            "Check that table borders are visible",
            "Verify image quality and resolution",
            "Consider manual table boundary marking"
        ]
        
        for suggestion in suggestions:
            para = doc.add_paragraph()
            para.add_run('• ').font.bold = True
            para.add_run(suggestion)
    
    def add_document_footer(self, doc: Document, metadata: Dict[str, Any], 
                          style_config: Dict[str, Any]):
        """Add professional document footer"""
        
        # Add spacing
        doc.add_paragraph()
        
        # Add separator line
        doc.add_paragraph('_' * 80).alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Footer information
        footer_para = doc.add_paragraph()
        footer_run = footer_para.add_run('Generated by Yark Tabular Extraction System')
        footer_run.font.name = style_config['font_name']
        footer_run.font.size = Pt(8)
        footer_run.italic = True
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Processing stats if available
        if metadata.get('processing_stats'):
            stats = metadata['processing_stats']
            stats_para = doc.add_paragraph()
            stats_text = f"Processing time: {stats.get('processing_time', 'N/A')}s | "
            stats_text += f"Confidence: {stats.get('confidence', 'N/A')} | "
            stats_text += f"Method: {stats.get('method', 'N/A')}"
            
            stats_run = stats_para.add_run(stats_text)
            stats_run.font.size = Pt(8)
            stats_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

# Global instance
enhanced_word_generator = EnhancedWordDocumentGenerator()
