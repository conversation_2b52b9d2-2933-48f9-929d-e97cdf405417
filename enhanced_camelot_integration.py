"""
Enhanced Camelot Integration for Yark Tabular Extraction
Provides robust PDF table extraction with comprehensive error handling and optimization
"""

import os
import tempfile
import shutil
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from PIL import Image
import logging

# Configure logging for Camelot
logging.getLogger('camelot').setLevel(logging.WARNING)

# Debug mode for enhanced logging
DEBUG_MODE = True

def debug_log(message, level="INFO"):
    """Enhanced debug logging for Enhanced Camelot extraction"""
    if DEBUG_MODE:
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] Enhanced Camelot {level}: {message}")

class EnhancedCamelotExtractor:
    """Enhanced Camelot table extractor with robust error handling and optimization"""
    
    def __init__(self):
        self.camelot_available = False
        self.temp_dir = None
        self.initialize_camelot()
        
    def initialize_camelot(self):
        """Initialize Camelot with proper error handling"""
        try:
            import camelot
            self.camelot = camelot
            self.camelot_available = True
            print("✅ Enhanced Camelot integration initialized successfully")
            
            # Create temporary directory for PDF conversions
            self.temp_dir = tempfile.mkdtemp(prefix="yark_camelot_")
            
        except ImportError as e:
            print("⚠️  Camelot not available - using fallback table extraction")
            print("   💡 Install with: pip install camelot-py[cv]")
            print(f"   📋 Error details: {e}")
            self.camelot_available = False
        except Exception as e:
            print(f"❌ Camelot initialization failed: {e}")
            self.camelot_available = False
    
    def is_available(self) -> bool:
        """Check if Camelot is available and properly initialized"""
        return self.camelot_available and self.temp_dir is not None
    
    def convert_image_to_pdf(self, image_path: str) -> Optional[str]:
        """Convert image to PDF with enhanced error handling and optimization"""
        if not self.is_available():
            return None
            
        try:
            print("    🔄 Converting image to PDF for Camelot processing...")
            
            # Open and validate image
            img = Image.open(image_path)
            print(f"    📊 Image format: {img.format}, Mode: {img.mode}, Size: {img.size}")
            
            # Optimize image for table extraction
            img = self.optimize_image_for_pdf(img)
            
            # Generate unique PDF filename
            pdf_filename = f"camelot_temp_{os.path.basename(image_path)}.pdf"
            pdf_path = os.path.join(self.temp_dir, pdf_filename)
            
            # Convert to RGB if necessary
            if img.mode not in ['RGB', 'L']:
                print(f"    🎨 Converting from {img.mode} to RGB")
                img = img.convert('RGB')
            
            # Save as high-quality PDF
            img.save(pdf_path, "PDF", resolution=300.0, quality=95, optimize=True)
            
            # Verify PDF was created successfully
            if os.path.exists(pdf_path) and os.path.getsize(pdf_path) > 0:
                print(f"    ✅ PDF created successfully: {os.path.basename(pdf_path)} ({os.path.getsize(pdf_path)} bytes)")
                return pdf_path
            else:
                print("    ❌ PDF creation failed - file not created or empty")
                return None
                
        except Exception as e:
            print(f"    ❌ Image to PDF conversion failed: {e}")
            return None
    
    def optimize_image_for_pdf(self, img: Image.Image) -> Image.Image:
        """Optimize image for better Camelot table extraction"""
        try:
            # Ensure minimum resolution for good table detection
            width, height = img.size
            min_dimension = 1200
            
            if width < min_dimension or height < min_dimension:
                # Scale up small images
                scale_factor = max(min_dimension / width, min_dimension / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                img = img.resize((new_width, new_height), Image.LANCZOS)
                print(f"    📏 Scaled image from {width}x{height} to {new_width}x{new_height}")
            
            # Enhance contrast for better line detection
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.2)
            
            return img
            
        except Exception as e:
            print(f"    ⚠️ Image optimization failed: {e}")
            return img
    
    def extract_tables_with_multiple_methods(self, pdf_path: str) -> Optional[List[List[str]]]:
        """Extract tables using multiple Camelot methods with fallback"""
        if not self.is_available():
            return None
        
        extraction_methods = [
            {'flavor': 'lattice', 'description': 'Lattice method (line-based)'},
            {'flavor': 'stream', 'description': 'Stream method (text-based)'},
            {'flavor': 'lattice', 'line_scale': 40, 'description': 'Lattice with adjusted line scale'},
            {'flavor': 'stream', 'edge_tol': 500, 'description': 'Stream with adjusted edge tolerance'}
        ]
        
        best_result = None
        best_accuracy = 0
        
        for method in extraction_methods:
            try:
                print(f"    🔍 Trying {method['description']}...")

                # Extract method parameters
                flavor = method.pop('flavor')
                description = method.pop('description')
                kwargs = method  # Remaining parameters

                debug_log(f"Testing method: {description} with flavor: {flavor}, params: {kwargs}")

                # Attempt extraction
                tables = self.camelot.read_pdf(
                    pdf_path,
                    pages='1',
                    flavor=flavor,
                    suppress_stdout=True,
                    **kwargs
                )

                debug_log(f"Method {description} returned {len(tables)} tables")
                
                if len(tables) > 0:
                    # Get the best table from this method
                    best_table = max(tables, key=lambda t: getattr(t, 'accuracy', 0))
                    accuracy = getattr(best_table, 'accuracy', 0)
                    
                    print(f"      ✅ Found {len(tables)} table(s), best accuracy: {accuracy:.2f}")
                    
                    if accuracy > best_accuracy:
                        best_accuracy = accuracy
                        best_result = {
                            'table': best_table,
                            'method': description,
                            'accuracy': accuracy,
                            'table_count': len(tables)
                        }
                        print(f"      🎯 New best result with {description}")
                else:
                    print(f"      ⚠️ No tables found with {description}")
                    
            except Exception as e:
                print(f"      ❌ {description} failed: {e}")
                continue
        
        if best_result:
            return self.process_camelot_result(best_result)
        else:
            print("    ❌ All Camelot extraction methods failed")
            return None
    
    def process_camelot_result(self, result: Dict[str, Any]) -> List[List[str]]:
        """Process and clean Camelot extraction result"""
        try:
            table = result['table']
            method = result['method']
            accuracy = result['accuracy']
            
            print(f"    📊 Processing best result from {method} (accuracy: {accuracy:.2f})")
            
            # Convert DataFrame to list format
            table_data = table.df.values.tolist()

            # Assess table quality before cleaning
            quality_result = self.assess_table_quality(table_data, accuracy)

            if not quality_result['passed']:
                print(f"    ❌ Table quality assessment failed (score: {quality_result['score']}/100)")
                print(f"    🔍 Issues: {', '.join(quality_result['issues'])}")
                return None

            # Clean the data
            cleaned_data = self.clean_camelot_data(table_data)

            if cleaned_data:
                rows = len(cleaned_data)
                cols = len(cleaned_data[0]) if cleaned_data else 0
                print(f"    ✅ Extracted table: {rows} rows × {cols} columns")
                print(f"    🎯 Final accuracy score: {accuracy:.2f}")
                print(f"    ✅ Quality assessment passed (score: {quality_result['score']}/100)")
                
                # Log sample of extracted data
                if rows > 0:
                    print("    📋 Sample data (first row):")
                    sample_row = cleaned_data[0][:3]  # First 3 cells
                    for i, cell in enumerate(sample_row):
                        print(f"      [{i+1}]: '{cell}'")
                
                return cleaned_data
            else:
                print("    ⚠️ Data cleaning resulted in empty table")
                return None
                
        except Exception as e:
            print(f"    ❌ Error processing Camelot result: {e}")
            return None
    
    def clean_camelot_data(self, raw_data: List[List[str]]) -> Optional[List[List[str]]]:
        """Clean and validate Camelot extracted data"""
        if not raw_data:
            return None
        
        cleaned_data = []
        
        for row_idx, row in enumerate(raw_data):
            cleaned_row = []
            
            for cell in row:
                # Convert to string and clean
                if cell is None or (isinstance(cell, float) and np.isnan(cell)):
                    cleaned_cell = ""
                else:
                    cleaned_cell = str(cell).strip()
                    
                    # Remove excessive whitespace
                    cleaned_cell = ' '.join(cleaned_cell.split())
                    
                    # Remove common Camelot artifacts
                    cleaned_cell = cleaned_cell.replace('\n', ' ')
                    cleaned_cell = cleaned_cell.replace('\r', ' ')
                
                cleaned_row.append(cleaned_cell)
            
            # Only add rows that have at least one non-empty cell
            if any(cell.strip() for cell in cleaned_row):
                cleaned_data.append(cleaned_row)
        
        # Ensure consistent column count
        if cleaned_data:
            max_cols = max(len(row) for row in cleaned_data)
            for row in cleaned_data:
                while len(row) < max_cols:
                    row.append("")
        
        # Validate minimum table requirements
        if len(cleaned_data) < 2:  # Need at least header + 1 data row
            print("    ⚠️ Insufficient rows for valid table")
            return None
        
        return cleaned_data

    def assess_table_quality(self, table_data, camelot_accuracy=None):
        """Comprehensive table quality assessment for extracted tables"""
        if not table_data or len(table_data) < 2:
            return {'score': 0, 'issues': ['Table has less than 2 rows'], 'passed': False}

        print("    🔍 Assessing table quality...")

        quality_score = 0
        max_score = 100
        issues = []

        # 1. Basic structure assessment (20 points)
        rows = len(table_data)
        cols = len(table_data[0]) if table_data else 0

        if rows >= 2:
            quality_score += 10
        else:
            issues.append(f"Too few rows: {rows}")

        if cols >= 2:
            quality_score += 10
        else:
            issues.append(f"Too few columns: {cols}")

        # 2. Column consistency (20 points)
        col_counts = [len(row) for row in table_data]
        if len(set(col_counts)) == 1:  # All rows have same column count
            quality_score += 20
        else:
            issues.append(f"Inconsistent column counts: {set(col_counts)}")

        # 3. Content density (20 points)
        total_cells = rows * cols
        empty_cells = sum(1 for row in table_data for cell in row if not str(cell).strip())
        content_ratio = (total_cells - empty_cells) / total_cells if total_cells > 0 else 0

        if content_ratio >= 0.7:
            quality_score += 20
        elif content_ratio >= 0.5:
            quality_score += 15
        elif content_ratio >= 0.3:
            quality_score += 10
        else:
            issues.append(f"Low content density: {content_ratio:.2f}")

        # 4. Camelot accuracy (20 points)
        if camelot_accuracy is not None:
            if camelot_accuracy >= 90:
                quality_score += 20
            elif camelot_accuracy >= 80:
                quality_score += 15
            elif camelot_accuracy >= 70:
                quality_score += 10
            elif camelot_accuracy >= 60:
                quality_score += 5
            else:
                issues.append(f"Low Camelot accuracy: {camelot_accuracy:.2f}")
        else:
            quality_score += 10  # Neutral score if no accuracy available

        # 5. Data type consistency (20 points)
        numeric_cols = 0
        text_cols = 0

        for col_idx in range(cols):
            col_data = [str(table_data[row_idx][col_idx]).strip()
                       for row_idx in range(rows)
                       if col_idx < len(table_data[row_idx])]

            # Check if column contains mostly numbers
            numeric_count = sum(1 for cell in col_data
                              if cell and (cell.replace('.', '').replace(',', '').replace('$', '').replace('-', '').isdigit()))

            if numeric_count > len(col_data) * 0.7:
                numeric_cols += 1
            elif len([cell for cell in col_data if cell]) > 0:
                text_cols += 1

        if numeric_cols > 0 or text_cols > 0:
            quality_score += 20
        else:
            issues.append("No clear data patterns detected")

        # Final assessment
        final_score = min(quality_score, max_score)
        passed = final_score >= 70  # Require 70% quality score

        quality_result = {
            'score': final_score,
            'issues': issues,
            'passed': passed,
            'details': {
                'rows': rows,
                'cols': cols,
                'content_ratio': content_ratio,
                'camelot_accuracy': camelot_accuracy,
                'numeric_cols': numeric_cols,
                'text_cols': text_cols
            }
        }

        print(f"      📊 Quality Score: {final_score}/100 ({'PASSED' if passed else 'FAILED'})")
        if issues:
            print(f"      ⚠️  Issues: {', '.join(issues[:3])}")  # Show first 3 issues

        return quality_result

    def extract_table_from_image(self, image_path: str) -> Optional[List[List[str]]]:
        """Main extraction method - convert image to PDF and extract table"""
        if not self.is_available():
            print("  ⚠️ Camelot not available, skipping extraction")
            return None
        
        print("  🐪 Starting enhanced Camelot table extraction...")
        
        try:
            # Convert image to PDF
            pdf_path = self.convert_image_to_pdf(image_path)
            
            if not pdf_path:
                print("  ❌ PDF conversion failed")
                return None
            
            # Extract tables using multiple methods
            table_data = self.extract_tables_with_multiple_methods(pdf_path)
            
            # Clean up temporary PDF
            self.cleanup_temp_file(pdf_path)
            
            if table_data:
                print("  ✅ Camelot extraction completed successfully")
                return table_data
            else:
                print("  ⚠️ Camelot extraction found no valid tables")
                return None
                
        except Exception as e:
            print(f"  ❌ Camelot extraction failed with error: {e}")
            return None
    
    def cleanup_temp_file(self, file_path: str):
        """Clean up a specific temporary file"""
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                print(f"    🗑️ Cleaned up temporary file: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"    ⚠️ Could not clean up {file_path}: {e}")
    
    def cleanup_all_temp_files(self):
        """Clean up all temporary files and directories"""
        try:
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                print("🗑️ Cleaned up all Camelot temporary files")
        except Exception as e:
            print(f"⚠️ Could not clean up temporary directory: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of Camelot integration"""
        status = {
            'available': self.camelot_available,
            'temp_dir': self.temp_dir,
            'temp_dir_exists': self.temp_dir and os.path.exists(self.temp_dir) if self.temp_dir else False
        }
        
        if self.camelot_available:
            try:
                status['camelot_version'] = getattr(self.camelot, '__version__', 'unknown')
            except:
                status['camelot_version'] = 'unknown'
        
        return status
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.cleanup_all_temp_files()

# Global instance
enhanced_camelot = EnhancedCamelotExtractor()
