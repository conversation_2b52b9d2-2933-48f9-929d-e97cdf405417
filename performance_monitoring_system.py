"""
Performance Monitoring and Analytics System for Yark Tabular Extraction
Provides comprehensive performance tracking, success rate monitoring, and bottleneck identification
"""

import time
import psutil
import threading
import json
import os
from typing import List, Dict, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import statistics
from datetime import datetime, timedelta

@dataclass
class ProcessingMetrics:
    """Individual processing metrics for a single file"""
    file_path: str
    file_size_mb: float
    image_dimensions: Tuple[int, int]
    processing_start_time: float
    processing_end_time: float
    processing_duration: float
    success: bool
    error_message: Optional[str]
    
    # Stage timings
    preprocessing_time: float
    ocr_time: float
    table_detection_time: float
    postprocessing_time: float
    document_creation_time: float
    
    # Quality metrics
    confidence_score: float
    table_rows: int
    table_columns: int
    text_regions_found: int
    
    # Resource usage
    peak_memory_mb: float
    cpu_usage_percent: float
    
    # Method information
    ocr_engine_used: str
    preprocessing_method: str
    table_detection_method: str

class PerformanceMonitor:
    """Comprehensive performance monitoring and analytics system"""
    
    def __init__(self, max_history_size: int = 1000):
        self.max_history_size = max_history_size
        self.processing_history = deque(maxlen=max_history_size)
        self.current_session_metrics = []
        self.session_start_time = time.time()
        
        # Real-time monitoring
        self.current_processing = {}
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Performance thresholds
        self.performance_thresholds = {
            'processing_time_warning': 30.0,  # seconds
            'processing_time_critical': 60.0,  # seconds
            'memory_usage_warning': 1024,  # MB
            'memory_usage_critical': 2048,  # MB
            'success_rate_warning': 0.8,  # 80%
            'success_rate_critical': 0.6   # 60%
        }
        
        # Analytics cache
        self.analytics_cache = {}
        self.cache_expiry = 300  # 5 minutes
        self.last_cache_update = 0
    
    def start_monitoring_session(self):
        """Start a new monitoring session"""
        self.session_start_time = time.time()
        self.current_session_metrics = []
        self.monitoring_active = True
        
        # Start background monitoring thread
        if not self.monitor_thread or not self.monitor_thread.is_alive():
            self.monitor_thread = threading.Thread(target=self._background_monitor, daemon=True)
            self.monitor_thread.start()
        
        print("📊 Performance monitoring session started")
    
    def stop_monitoring_session(self) -> Dict[str, Any]:
        """Stop monitoring session and return session summary"""
        self.monitoring_active = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=1.0)
        
        session_duration = time.time() - self.session_start_time
        session_summary = self.generate_session_summary(session_duration)
        
        print("📊 Performance monitoring session stopped")
        return session_summary
    
    def start_file_processing(self, file_path: str, file_size_mb: float, 
                            image_dimensions: Tuple[int, int]) -> str:
        """Start monitoring for a specific file processing"""
        
        processing_id = f"{file_path}_{time.time()}"
        
        self.current_processing[processing_id] = {
            'file_path': file_path,
            'file_size_mb': file_size_mb,
            'image_dimensions': image_dimensions,
            'start_time': time.time(),
            'stage_times': {},
            'resource_usage': [],
            'methods_used': {}
        }
        
        return processing_id
    
    def record_stage_timing(self, processing_id: str, stage: str, duration: float):
        """Record timing for a specific processing stage"""
        
        if processing_id in self.current_processing:
            self.current_processing[processing_id]['stage_times'][stage] = duration
    
    def record_method_used(self, processing_id: str, category: str, method: str):
        """Record which method was used for a processing category"""
        
        if processing_id in self.current_processing:
            self.current_processing[processing_id]['methods_used'][category] = method
    
    def finish_file_processing(self, processing_id: str, success: bool, 
                             error_message: Optional[str] = None,
                             quality_metrics: Optional[Dict[str, Any]] = None) -> ProcessingMetrics:
        """Finish monitoring for a specific file and create metrics"""
        
        if processing_id not in self.current_processing:
            return None
        
        processing_data = self.current_processing.pop(processing_id)
        end_time = time.time()
        
        # Calculate resource usage statistics
        resource_usage = processing_data.get('resource_usage', [])
        peak_memory = max([r['memory_mb'] for r in resource_usage]) if resource_usage else 0
        avg_cpu = statistics.mean([r['cpu_percent'] for r in resource_usage]) if resource_usage else 0
        
        # Create metrics object
        metrics = ProcessingMetrics(
            file_path=processing_data['file_path'],
            file_size_mb=processing_data['file_size_mb'],
            image_dimensions=processing_data['image_dimensions'],
            processing_start_time=processing_data['start_time'],
            processing_end_time=end_time,
            processing_duration=end_time - processing_data['start_time'],
            success=success,
            error_message=error_message,
            
            # Stage timings
            preprocessing_time=processing_data['stage_times'].get('preprocessing', 0),
            ocr_time=processing_data['stage_times'].get('ocr', 0),
            table_detection_time=processing_data['stage_times'].get('table_detection', 0),
            postprocessing_time=processing_data['stage_times'].get('postprocessing', 0),
            document_creation_time=processing_data['stage_times'].get('document_creation', 0),
            
            # Quality metrics
            confidence_score=quality_metrics.get('confidence_score', 0) if quality_metrics else 0,
            table_rows=quality_metrics.get('table_rows', 0) if quality_metrics else 0,
            table_columns=quality_metrics.get('table_columns', 0) if quality_metrics else 0,
            text_regions_found=quality_metrics.get('text_regions_found', 0) if quality_metrics else 0,
            
            # Resource usage
            peak_memory_mb=peak_memory,
            cpu_usage_percent=avg_cpu,
            
            # Method information
            ocr_engine_used=processing_data['methods_used'].get('ocr_engine', 'unknown'),
            preprocessing_method=processing_data['methods_used'].get('preprocessing', 'unknown'),
            table_detection_method=processing_data['methods_used'].get('table_detection', 'unknown')
        )
        
        # Add to history
        self.processing_history.append(metrics)
        self.current_session_metrics.append(metrics)
        
        # Check for performance issues
        self._check_performance_thresholds(metrics)
        
        return metrics
    
    def _background_monitor(self):
        """Background thread for monitoring system resources"""
        
        while self.monitoring_active:
            try:
                # Monitor resource usage for active processing
                current_time = time.time()
                process = psutil.Process()
                memory_info = process.memory_info()
                cpu_percent = process.cpu_percent()
                
                resource_snapshot = {
                    'timestamp': current_time,
                    'memory_mb': memory_info.rss / (1024 * 1024),
                    'cpu_percent': cpu_percent
                }
                
                # Add to all active processing records
                for processing_id in list(self.current_processing.keys()):
                    self.current_processing[processing_id]['resource_usage'].append(resource_snapshot)
                
                time.sleep(1)  # Monitor every second
                
            except Exception as e:
                print(f"⚠️ Background monitoring error: {e}")
                time.sleep(5)  # Wait longer on error
    
    def _check_performance_thresholds(self, metrics: ProcessingMetrics):
        """Check if performance metrics exceed warning thresholds"""
        
        warnings = []
        
        # Processing time warnings
        if metrics.processing_duration > self.performance_thresholds['processing_time_critical']:
            warnings.append(f"CRITICAL: Processing time {metrics.processing_duration:.1f}s exceeds critical threshold")
        elif metrics.processing_duration > self.performance_thresholds['processing_time_warning']:
            warnings.append(f"WARNING: Processing time {metrics.processing_duration:.1f}s exceeds warning threshold")
        
        # Memory usage warnings
        if metrics.peak_memory_mb > self.performance_thresholds['memory_usage_critical']:
            warnings.append(f"CRITICAL: Memory usage {metrics.peak_memory_mb:.1f}MB exceeds critical threshold")
        elif metrics.peak_memory_mb > self.performance_thresholds['memory_usage_warning']:
            warnings.append(f"WARNING: Memory usage {metrics.peak_memory_mb:.1f}MB exceeds warning threshold")
        
        # Print warnings
        for warning in warnings:
            print(f"    ⚠️ {warning}")
    
    def get_real_time_analytics(self) -> Dict[str, Any]:
        """Get real-time analytics for current session"""
        
        if not self.current_session_metrics:
            return {'status': 'no_data', 'message': 'No processing data available'}
        
        # Calculate current session statistics
        successful_files = [m for m in self.current_session_metrics if m.success]
        failed_files = [m for m in self.current_session_metrics if not m.success]
        
        total_files = len(self.current_session_metrics)
        success_rate = len(successful_files) / total_files if total_files > 0 else 0
        
        # Processing time statistics
        processing_times = [m.processing_duration for m in self.current_session_metrics]
        avg_processing_time = statistics.mean(processing_times) if processing_times else 0
        
        # Memory usage statistics
        memory_usage = [m.peak_memory_mb for m in self.current_session_metrics]
        avg_memory_usage = statistics.mean(memory_usage) if memory_usage else 0
        peak_memory_usage = max(memory_usage) if memory_usage else 0
        
        return {
            'session_duration': time.time() - self.session_start_time,
            'total_files_processed': total_files,
            'successful_files': len(successful_files),
            'failed_files': len(failed_files),
            'success_rate': success_rate,
            'avg_processing_time': avg_processing_time,
            'avg_memory_usage': avg_memory_usage,
            'peak_memory_usage': peak_memory_usage,
            'files_per_minute': total_files / ((time.time() - self.session_start_time) / 60) if total_files > 0 else 0
        }
    
    def generate_comprehensive_analytics(self) -> Dict[str, Any]:
        """Generate comprehensive analytics from all historical data"""
        
        # Check cache validity
        current_time = time.time()
        if (current_time - self.last_cache_update) < self.cache_expiry and self.analytics_cache:
            return self.analytics_cache
        
        if not self.processing_history:
            return {'status': 'no_data', 'message': 'No historical data available'}
        
        print("📊 Generating comprehensive analytics...")
        
        # Convert to list for analysis
        all_metrics = list(self.processing_history)
        successful_metrics = [m for m in all_metrics if m.success]
        failed_metrics = [m for m in all_metrics if not m.success]
        
        # Overall statistics
        overall_stats = self._calculate_overall_statistics(all_metrics, successful_metrics, failed_metrics)
        
        # Performance trends
        performance_trends = self._analyze_performance_trends(all_metrics)
        
        # Bottleneck analysis
        bottleneck_analysis = self._analyze_bottlenecks(successful_metrics)
        
        # Method effectiveness
        method_effectiveness = self._analyze_method_effectiveness(all_metrics)
        
        # Resource usage patterns
        resource_patterns = self._analyze_resource_patterns(all_metrics)
        
        # Error analysis
        error_analysis = self._analyze_errors(failed_metrics)
        
        analytics = {
            'generated_at': datetime.now().isoformat(),
            'data_period': {
                'start': datetime.fromtimestamp(min(m.processing_start_time for m in all_metrics)).isoformat(),
                'end': datetime.fromtimestamp(max(m.processing_end_time for m in all_metrics)).isoformat(),
                'total_files': len(all_metrics)
            },
            'overall_statistics': overall_stats,
            'performance_trends': performance_trends,
            'bottleneck_analysis': bottleneck_analysis,
            'method_effectiveness': method_effectiveness,
            'resource_usage_patterns': resource_patterns,
            'error_analysis': error_analysis
        }
        
        # Cache the results
        self.analytics_cache = analytics
        self.last_cache_update = current_time
        
        return analytics
    
    def _calculate_overall_statistics(self, all_metrics: List[ProcessingMetrics], 
                                    successful_metrics: List[ProcessingMetrics],
                                    failed_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Calculate overall performance statistics"""
        
        total_files = len(all_metrics)
        success_rate = len(successful_metrics) / total_files if total_files > 0 else 0
        
        # Processing time statistics
        processing_times = [m.processing_duration for m in successful_metrics]
        
        # Quality statistics
        confidence_scores = [m.confidence_score for m in successful_metrics if m.confidence_score > 0]
        table_sizes = [(m.table_rows, m.table_columns) for m in successful_metrics if m.table_rows > 0]
        
        return {
            'total_files_processed': total_files,
            'successful_files': len(successful_metrics),
            'failed_files': len(failed_metrics),
            'success_rate': success_rate,
            'avg_processing_time': statistics.mean(processing_times) if processing_times else 0,
            'median_processing_time': statistics.median(processing_times) if processing_times else 0,
            'min_processing_time': min(processing_times) if processing_times else 0,
            'max_processing_time': max(processing_times) if processing_times else 0,
            'avg_confidence_score': statistics.mean(confidence_scores) if confidence_scores else 0,
            'avg_table_size': {
                'rows': statistics.mean([size[0] for size in table_sizes]) if table_sizes else 0,
                'columns': statistics.mean([size[1] for size in table_sizes]) if table_sizes else 0
            }
        }
    
    def _analyze_performance_trends(self, all_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze performance trends over time"""
        
        # Sort by processing time
        sorted_metrics = sorted(all_metrics, key=lambda m: m.processing_start_time)
        
        # Calculate moving averages
        window_size = min(10, len(sorted_metrics))
        moving_averages = []
        
        for i in range(len(sorted_metrics) - window_size + 1):
            window = sorted_metrics[i:i + window_size]
            avg_time = statistics.mean([m.processing_duration for m in window])
            success_rate = sum(1 for m in window if m.success) / len(window)
            
            moving_averages.append({
                'timestamp': window[-1].processing_start_time,
                'avg_processing_time': avg_time,
                'success_rate': success_rate
            })
        
        return {
            'moving_averages': moving_averages,
            'trend_analysis': 'improving' if len(moving_averages) > 1 and 
                           moving_averages[-1]['avg_processing_time'] < moving_averages[0]['avg_processing_time'] 
                           else 'stable'
        }
    
    def _analyze_bottlenecks(self, successful_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze processing bottlenecks"""
        
        if not successful_metrics:
            return {'bottlenecks': []}
        
        # Calculate average time for each stage
        stage_times = {
            'preprocessing': [m.preprocessing_time for m in successful_metrics if m.preprocessing_time > 0],
            'ocr': [m.ocr_time for m in successful_metrics if m.ocr_time > 0],
            'table_detection': [m.table_detection_time for m in successful_metrics if m.table_detection_time > 0],
            'postprocessing': [m.postprocessing_time for m in successful_metrics if m.postprocessing_time > 0],
            'document_creation': [m.document_creation_time for m in successful_metrics if m.document_creation_time > 0]
        }
        
        stage_averages = {}
        for stage, times in stage_times.items():
            if times:
                stage_averages[stage] = {
                    'avg_time': statistics.mean(times),
                    'max_time': max(times),
                    'percentage_of_total': 0  # Will calculate below
                }
        
        # Calculate percentage of total processing time
        total_avg_time = sum(data['avg_time'] for data in stage_averages.values())
        for stage_data in stage_averages.values():
            stage_data['percentage_of_total'] = (stage_data['avg_time'] / total_avg_time * 100) if total_avg_time > 0 else 0
        
        # Identify bottlenecks (stages taking >30% of total time)
        bottlenecks = [
            {'stage': stage, 'avg_time': data['avg_time'], 'percentage': data['percentage_of_total']}
            for stage, data in stage_averages.items()
            if data['percentage_of_total'] > 30
        ]
        
        return {
            'stage_breakdown': stage_averages,
            'bottlenecks': bottlenecks,
            'recommendations': self._generate_bottleneck_recommendations(bottlenecks)
        }
    
    def _analyze_method_effectiveness(self, all_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze effectiveness of different processing methods"""
        
        method_stats = defaultdict(lambda: {'total': 0, 'successful': 0, 'avg_time': 0, 'times': []})
        
        for metric in all_metrics:
            # OCR engine effectiveness
            engine = metric.ocr_engine_used
            method_stats[f"ocr_{engine}"]['total'] += 1
            method_stats[f"ocr_{engine}"]['times'].append(metric.processing_duration)
            if metric.success:
                method_stats[f"ocr_{engine}"]['successful'] += 1
        
        # Calculate success rates and average times
        effectiveness_analysis = {}
        for method, stats in method_stats.items():
            if stats['total'] > 0:
                effectiveness_analysis[method] = {
                    'success_rate': stats['successful'] / stats['total'],
                    'avg_processing_time': statistics.mean(stats['times']),
                    'total_uses': stats['total']
                }
        
        return effectiveness_analysis
    
    def _analyze_resource_patterns(self, all_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze resource usage patterns"""
        
        memory_usage = [m.peak_memory_mb for m in all_metrics if m.peak_memory_mb > 0]
        cpu_usage = [m.cpu_usage_percent for m in all_metrics if m.cpu_usage_percent > 0]
        
        return {
            'memory_usage': {
                'avg_mb': statistics.mean(memory_usage) if memory_usage else 0,
                'peak_mb': max(memory_usage) if memory_usage else 0,
                'min_mb': min(memory_usage) if memory_usage else 0
            },
            'cpu_usage': {
                'avg_percent': statistics.mean(cpu_usage) if cpu_usage else 0,
                'peak_percent': max(cpu_usage) if cpu_usage else 0
            }
        }
    
    def _analyze_errors(self, failed_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze error patterns"""
        
        if not failed_metrics:
            return {'error_patterns': [], 'common_errors': []}
        
        # Group errors by message
        error_counts = defaultdict(int)
        for metric in failed_metrics:
            if metric.error_message:
                error_counts[metric.error_message] += 1
        
        # Sort by frequency
        common_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'total_errors': len(failed_metrics),
            'common_errors': [{'error': error, 'count': count} for error, count in common_errors[:10]],
            'error_rate_by_file_size': self._analyze_error_rate_by_file_size(failed_metrics)
        }
    
    def _analyze_error_rate_by_file_size(self, failed_metrics: List[ProcessingMetrics]) -> Dict[str, Any]:
        """Analyze error rates by file size"""
        
        # Group by file size ranges
        size_ranges = {
            'small': (0, 1),      # 0-1 MB
            'medium': (1, 5),     # 1-5 MB
            'large': (5, 20),     # 5-20 MB
            'very_large': (20, float('inf'))  # >20 MB
        }
        
        range_errors = defaultdict(int)
        for metric in failed_metrics:
            for range_name, (min_size, max_size) in size_ranges.items():
                if min_size <= metric.file_size_mb < max_size:
                    range_errors[range_name] += 1
                    break
        
        return dict(range_errors)
    
    def _generate_bottleneck_recommendations(self, bottlenecks: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations based on identified bottlenecks"""
        
        recommendations = []
        
        for bottleneck in bottlenecks:
            stage = bottleneck['stage']
            
            if stage == 'ocr':
                recommendations.append("Consider using faster OCR engines or parallel processing for OCR operations")
            elif stage == 'preprocessing':
                recommendations.append("Optimize image preprocessing pipeline or reduce preprocessing complexity")
            elif stage == 'table_detection':
                recommendations.append("Improve table detection algorithms or use more efficient detection methods")
            elif stage == 'postprocessing':
                recommendations.append("Streamline post-processing operations or implement caching")
            elif stage == 'document_creation':
                recommendations.append("Optimize Word document generation or use templates")
        
        return recommendations
    
    def generate_session_summary(self, session_duration: float) -> Dict[str, Any]:
        """Generate summary for the current session"""
        
        if not self.current_session_metrics:
            return {'status': 'no_data'}
        
        successful = [m for m in self.current_session_metrics if m.success]
        failed = [m for m in self.current_session_metrics if not m.success]
        
        return {
            'session_duration': session_duration,
            'total_files': len(self.current_session_metrics),
            'successful_files': len(successful),
            'failed_files': len(failed),
            'success_rate': len(successful) / len(self.current_session_metrics),
            'avg_processing_time': statistics.mean([m.processing_duration for m in successful]) if successful else 0,
            'total_processing_time': sum(m.processing_duration for m in self.current_session_metrics),
            'throughput': len(self.current_session_metrics) / session_duration * 60 if session_duration > 0 else 0  # files per minute
        }
    
    def export_analytics_report(self, file_path: str):
        """Export comprehensive analytics to JSON file"""
        
        analytics = self.generate_comprehensive_analytics()
        
        with open(file_path, 'w') as f:
            json.dump(analytics, f, indent=2, default=str)
        
        print(f"📊 Analytics report exported to: {file_path}")
    
    def print_performance_summary(self):
        """Print a formatted performance summary"""
        
        analytics = self.get_real_time_analytics()
        
        if analytics.get('status') == 'no_data':
            print("📊 No performance data available")
            return
        
        print("\n" + "="*60)
        print("📊 PERFORMANCE MONITORING SUMMARY")
        print("="*60)
        
        print(f"📁 Files processed: {analytics['total_files_processed']}")
        print(f"✅ Success rate: {analytics['success_rate']:.1%}")
        print(f"⏱️ Average processing time: {analytics['avg_processing_time']:.2f} seconds")
        print(f"🚀 Throughput: {analytics['files_per_minute']:.1f} files/minute")
        print(f"💾 Average memory usage: {analytics['avg_memory_usage']:.1f} MB")
        print(f"📈 Peak memory usage: {analytics['peak_memory_usage']:.1f} MB")
        print(f"⏰ Session duration: {analytics['session_duration']:.1f} seconds")
        
        print("="*60)

# Global instance
performance_monitor = PerformanceMonitor()
