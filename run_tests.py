#!/usr/bin/env python3
"""
Test Runner for Yark Tabular Extraction System
Runs comprehensive tests and generates reports
"""

import os
import sys
import argparse
import tempfile
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path to import modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    parser = argparse.ArgumentParser(description='Run comprehensive tests for Yark Tabular Extraction')
    parser.add_argument('--test-type', choices=['unit', 'integration', 'performance', 'regression', 'all'], 
                       default='all', help='Type of tests to run')
    parser.add_argument('--generate-data', action='store_true', 
                       help='Generate synthetic test data')
    parser.add_argument('--test-data-dir', default='test_data', 
                       help='Directory containing test data')
    parser.add_argument('--output-dir', default='test_results', 
                       help='Directory for test results')
    parser.add_argument('--baseline-file', default='test_baselines.json',
                       help='File containing regression test baselines')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Verbose output')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🧪 Yark Tabular Extraction - Comprehensive Testing Suite")
    print("="*60)
    
    try:
        from comprehensive_testing_suite import test_suite, TestDataGenerator
        
        all_results = []
        
        # Generate test data if requested
        if args.generate_data:
            print("📊 Generating synthetic test data...")
            generator = TestDataGenerator()
            generator.create_test_dataset(args.test_data_dir, num_samples=5)
            print(f"✅ Test data generated in {args.test_data_dir}")
        
        # Run unit tests
        if args.test_type in ['unit', 'all']:
            print("\n🧪 Running unit tests...")
            unit_results = test_suite.run_unit_tests()
            all_results.extend(unit_results)
        
        # Run integration tests
        if args.test_type in ['integration', 'all']:
            print("\n🔗 Running integration tests...")
            if not os.path.exists(args.test_data_dir):
                print(f"⚠️ Test data directory not found: {args.test_data_dir}")
                print("💡 Use --generate-data to create test data")
            else:
                integration_results = test_suite.run_integration_tests(args.test_data_dir)
                all_results.extend(integration_results)
        
        # Run performance benchmarks
        if args.test_type in ['performance', 'all']:
            print("\n⚡ Running performance benchmarks...")
            benchmarks = test_suite.run_performance_benchmarks()
            
            # Save benchmark results
            benchmark_file = os.path.join(args.output_dir, f"benchmarks_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            import json
            with open(benchmark_file, 'w') as f:
                json.dump(benchmarks, f, indent=2, default=str)
            print(f"📊 Benchmark results saved to {benchmark_file}")
        
        # Run regression tests
        if args.test_type in ['regression', 'all']:
            print("\n🔄 Running regression tests...")
            if not os.path.exists(args.baseline_file):
                print(f"⚠️ Baseline file not found: {args.baseline_file}")
                print("💡 Run tests first to establish baselines")
            else:
                regression_results = test_suite.run_regression_tests(args.baseline_file)
                all_results.extend(regression_results)
        
        # Generate comprehensive report
        if all_results:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(args.output_dir, f"test_report_{timestamp}.json")
            
            report = test_suite.generate_test_report(all_results, report_file)
            test_suite.print_test_summary(all_results)
            
            print(f"\n📄 Detailed test report saved to: {report_file}")
            
            # Print summary statistics
            summary = report['summary']
            if summary['success_rate'] >= 0.9:
                print("🎉 Excellent! Test suite passed with high success rate")
            elif summary['success_rate'] >= 0.7:
                print("✅ Good! Most tests passed, some issues to address")
            else:
                print("⚠️ Warning! Low success rate, significant issues detected")
        
        else:
            print("⚠️ No tests were executed")
    
    except ImportError as e:
        print(f"❌ Failed to import testing modules: {e}")
        print("💡 Make sure all dependencies are installed")
        sys.exit(1)
    
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
