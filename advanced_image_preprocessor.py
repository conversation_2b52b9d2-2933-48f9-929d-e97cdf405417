"""
Advanced Image Preprocessing Pipeline for Yark Tabular Extraction
Provides comprehensive image enhancement specifically optimized for table OCR
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import Tuple, Dict, Any, Optional
import math

class AdvancedImagePreprocessor:
    """Advanced image preprocessing with adaptive algorithms for table OCR optimization"""
    
    def __init__(self):
        self.preprocessing_profiles = {
            'financial_tables': {
                'contrast_enhancement': 1.3,
                'sharpness_enhancement': 1.2,
                'noise_reduction': 'moderate',
                'skew_correction': True,
                'adaptive_threshold': True,
                'line_enhancement': True
            },
            'mathematical_tables': {
                'contrast_enhancement': 1.4,
                'sharpness_enhancement': 1.5,
                'noise_reduction': 'aggressive',
                'skew_correction': True,
                'adaptive_threshold': True,
                'line_enhancement': False
            },
            'handwritten_tables': {
                'contrast_enhancement': 1.2,
                'sharpness_enhancement': 1.1,
                'noise_reduction': 'light',
                'skew_correction': True,
                'adaptive_threshold': False,
                'line_enhancement': False
            },
            'low_quality': {
                'contrast_enhancement': 1.5,
                'sharpness_enhancement': 1.3,
                'noise_reduction': 'aggressive',
                'skew_correction': True,
                'adaptive_threshold': True,
                'line_enhancement': True
            },
            'high_quality': {
                'contrast_enhancement': 1.1,
                'sharpness_enhancement': 1.0,
                'noise_reduction': 'light',
                'skew_correction': False,
                'adaptive_threshold': False,
                'line_enhancement': False
            }
        }
    
    def analyze_image_quality(self, image: Image.Image) -> Dict[str, Any]:
        """Comprehensive image quality analysis"""
        img_array = np.array(image.convert('L'))
        height, width = img_array.shape
        
        # Sharpness analysis (Laplacian variance)
        laplacian_var = cv2.Laplacian(img_array, cv2.CV_64F).var()
        
        # Contrast analysis
        contrast = np.std(img_array)
        
        # Brightness analysis
        brightness = np.mean(img_array)
        
        # Noise estimation
        blur_kernel = np.ones((3,3), np.float32) / 9
        blurred = cv2.filter2D(img_array, -1, blur_kernel)
        noise_level = np.mean(np.abs(img_array.astype(float) - blurred.astype(float)))
        
        # Skew detection
        skew_angle = self.detect_skew_angle(img_array)
        
        # Resolution assessment
        dpi_estimate = self.estimate_dpi(image)
        
        # Overall quality score
        quality_score = self.calculate_quality_score(
            laplacian_var, contrast, brightness, noise_level, dpi_estimate
        )
        
        return {
            'sharpness': laplacian_var,
            'contrast': contrast,
            'brightness': brightness,
            'noise_level': noise_level,
            'skew_angle': skew_angle,
            'estimated_dpi': dpi_estimate,
            'quality_score': quality_score,
            'resolution': (width, height),
            'needs_enhancement': quality_score < 0.7
        }
    
    def detect_skew_angle(self, img_array: np.ndarray) -> float:
        """Detect document skew angle using Hough line transform"""
        try:
            # Apply edge detection
            edges = cv2.Canny(img_array, 50, 150, apertureSize=3)
            
            # Detect lines using Hough transform
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is not None:
                angles = []
                for rho, theta in lines[:20]:  # Consider top 20 lines
                    angle = theta * 180 / np.pi
                    # Convert to skew angle (-45 to 45 degrees)
                    if angle > 90:
                        angle = angle - 180
                    elif angle > 45:
                        angle = angle - 90
                    angles.append(angle)
                
                if angles:
                    # Use median angle to avoid outliers
                    return np.median(angles)
            
            return 0.0
        except:
            return 0.0
    
    def estimate_dpi(self, image: Image.Image) -> int:
        """Estimate image DPI based on content analysis"""
        # Get DPI from image metadata if available
        if hasattr(image, 'info') and 'dpi' in image.info:
            return int(image.info['dpi'][0])
        
        # Estimate based on image size and typical document sizes
        width, height = image.size
        
        # Assume A4 document (8.27 x 11.69 inches) for estimation
        dpi_width = width / 8.27
        dpi_height = height / 11.69
        
        estimated_dpi = int((dpi_width + dpi_height) / 2)
        
        # Clamp to reasonable range
        return max(72, min(600, estimated_dpi))
    
    def calculate_quality_score(self, sharpness: float, contrast: float, 
                              brightness: float, noise: float, dpi: int) -> float:
        """Calculate overall image quality score (0-1)"""
        # Normalize metrics
        sharpness_score = min(1.0, sharpness / 500)  # Good sharpness > 500
        contrast_score = min(1.0, contrast / 80)     # Good contrast > 80
        brightness_score = 1.0 - abs(brightness - 128) / 128  # Optimal brightness ~128
        noise_score = max(0.0, 1.0 - noise / 30)    # Low noise < 30
        dpi_score = min(1.0, dpi / 300)             # Good DPI > 300
        
        # Weighted average
        quality_score = (
            sharpness_score * 0.3 +
            contrast_score * 0.25 +
            brightness_score * 0.2 +
            noise_score * 0.15 +
            dpi_score * 0.1
        )
        
        return quality_score
    
    def select_preprocessing_profile(self, image: Image.Image, content_type: str = "general") -> str:
        """Select optimal preprocessing profile based on image analysis and content type"""
        quality_analysis = self.analyze_image_quality(image)
        
        # Content-based selection
        if content_type == "mathematical":
            return "mathematical_tables"
        elif content_type == "financial":
            return "financial_tables"
        elif content_type == "handwritten":
            return "handwritten_tables"
        
        # Quality-based selection
        if quality_analysis['quality_score'] < 0.4:
            return "low_quality"
        elif quality_analysis['quality_score'] > 0.8:
            return "high_quality"
        else:
            return "financial_tables"  # Default for most table processing
    
    def correct_skew(self, image: Image.Image, angle: float) -> Image.Image:
        """Correct document skew"""
        if abs(angle) < 0.5:  # Skip correction for minimal skew
            return image
        
        print(f"    📐 Correcting skew: {angle:.2f} degrees")
        
        # Convert to OpenCV format
        img_array = np.array(image)
        height, width = img_array.shape[:2]
        
        # Calculate rotation matrix
        center = (width // 2, height // 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # Apply rotation
        corrected = cv2.warpAffine(img_array, rotation_matrix, (width, height), 
                                 flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
        
        return Image.fromarray(corrected)
    
    def enhance_contrast_adaptive(self, image: Image.Image, enhancement_factor: float) -> Image.Image:
        """Apply adaptive contrast enhancement"""
        print(f"    🔆 Enhancing contrast: {enhancement_factor}x")
        
        # Convert to OpenCV for CLAHE
        img_array = np.array(image.convert('L'))
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(img_array)
        
        # Additional contrast enhancement if needed
        if enhancement_factor > 1.0:
            enhanced = cv2.convertScaleAbs(enhanced, alpha=enhancement_factor, beta=0)
        
        return Image.fromarray(enhanced)
    
    def reduce_noise(self, image: Image.Image, noise_level: str) -> Image.Image:
        """Apply noise reduction based on specified level"""
        print(f"    🔇 Reducing noise: {noise_level}")
        
        img_array = np.array(image.convert('L'))
        
        if noise_level == "light":
            # Gaussian blur with small kernel
            denoised = cv2.GaussianBlur(img_array, (3, 3), 0)
        elif noise_level == "moderate":
            # Non-local means denoising
            denoised = cv2.fastNlMeansDenoising(img_array, None, 10, 7, 21)
        elif noise_level == "aggressive":
            # Bilateral filter for edge-preserving smoothing
            denoised = cv2.bilateralFilter(img_array, 9, 75, 75)
        else:
            return image
        
        return Image.fromarray(denoised)
    
    def enhance_sharpness(self, image: Image.Image, sharpness_factor: float) -> Image.Image:
        """Enhance image sharpness"""
        if sharpness_factor <= 1.0:
            return image
        
        print(f"    ⚡ Enhancing sharpness: {sharpness_factor}x")
        
        # Use PIL's ImageEnhance for basic sharpening
        enhancer = ImageEnhance.Sharpness(image)
        sharpened = enhancer.enhance(sharpness_factor)
        
        return sharpened
    
    def apply_adaptive_threshold(self, image: Image.Image) -> Image.Image:
        """Apply adaptive thresholding for better text separation"""
        print("    📊 Applying adaptive thresholding")
        
        img_array = np.array(image.convert('L'))
        
        # Apply adaptive threshold
        adaptive_thresh = cv2.adaptiveThreshold(
            img_array, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        return Image.fromarray(adaptive_thresh)
    
    def enhance_table_lines(self, image: Image.Image) -> Image.Image:
        """Enhance table lines and borders"""
        print("    📋 Enhancing table lines")
        
        img_array = np.array(image.convert('L'))
        
        # Detect horizontal lines
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        horizontal_lines = cv2.morphologyEx(img_array, cv2.MORPH_OPEN, horizontal_kernel)
        
        # Detect vertical lines
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
        vertical_lines = cv2.morphologyEx(img_array, cv2.MORPH_OPEN, vertical_kernel)
        
        # Combine lines with original image
        lines_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
        enhanced = cv2.addWeighted(img_array, 0.8, lines_mask, 0.2, 0.0)
        
        return Image.fromarray(enhanced)
    
    def preprocess_image(self, image: Image.Image, content_type: str = "general", 
                        custom_profile: Optional[Dict] = None) -> Tuple[Image.Image, Dict[str, Any]]:
        """Main preprocessing pipeline with comprehensive enhancements"""
        print("  🔧 Starting advanced image preprocessing...")
        
        # Analyze image quality
        quality_analysis = self.analyze_image_quality(image)
        print(f"    📊 Image quality score: {quality_analysis['quality_score']:.2f}")
        
        # Select preprocessing profile
        if custom_profile:
            profile = custom_profile
            profile_name = "custom"
        else:
            profile_name = self.select_preprocessing_profile(image, content_type)
            profile = self.preprocessing_profiles[profile_name]
        
        print(f"    🎯 Using preprocessing profile: {profile_name}")
        
        processed_image = image.copy()
        processing_steps = []
        
        # Step 1: Skew correction
        if profile.get('skew_correction', False) and abs(quality_analysis['skew_angle']) > 0.5:
            processed_image = self.correct_skew(processed_image, quality_analysis['skew_angle'])
            processing_steps.append(f"skew_correction_{quality_analysis['skew_angle']:.1f}deg")
        
        # Step 2: Noise reduction
        noise_level = profile.get('noise_reduction', 'moderate')
        if noise_level != 'none':
            processed_image = self.reduce_noise(processed_image, noise_level)
            processing_steps.append(f"noise_reduction_{noise_level}")
        
        # Step 3: Contrast enhancement
        contrast_factor = profile.get('contrast_enhancement', 1.0)
        if contrast_factor > 1.0:
            processed_image = self.enhance_contrast_adaptive(processed_image, contrast_factor)
            processing_steps.append(f"contrast_{contrast_factor}x")
        
        # Step 4: Sharpness enhancement
        sharpness_factor = profile.get('sharpness_enhancement', 1.0)
        if sharpness_factor > 1.0:
            processed_image = self.enhance_sharpness(processed_image, sharpness_factor)
            processing_steps.append(f"sharpness_{sharpness_factor}x")
        
        # Step 5: Table line enhancement
        if profile.get('line_enhancement', False):
            processed_image = self.enhance_table_lines(processed_image)
            processing_steps.append("line_enhancement")
        
        # Step 6: Adaptive thresholding
        if profile.get('adaptive_threshold', False):
            processed_image = self.apply_adaptive_threshold(processed_image)
            processing_steps.append("adaptive_threshold")
        
        processing_info = {
            'original_quality': quality_analysis,
            'profile_used': profile_name,
            'processing_steps': processing_steps,
            'profile_settings': profile
        }
        
        print(f"    ✅ Preprocessing completed: {len(processing_steps)} steps applied")
        
        return processed_image, processing_info

# Global instance
advanced_preprocessor = AdvancedImagePreprocessor()
