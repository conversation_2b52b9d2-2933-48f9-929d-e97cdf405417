"""
Quality Assessment and Confidence Scoring System for Yark Tabular Extraction
Provides comprehensive quality metrics and automatic retry mechanisms
"""

import re
import statistics
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from PIL import Image

class QualityAssessmentSystem:
    """Advanced quality assessment with multi-dimensional scoring and retry logic"""
    
    def __init__(self):
        self.quality_thresholds = {
            'excellent': 0.9,
            'good': 0.75,
            'acceptable': 0.6,
            'poor': 0.4,
            'unacceptable': 0.0
        }
        
        self.retry_thresholds = {
            'overall_quality': 0.5,
            'text_confidence': 0.4,
            'structure_confidence': 0.3,
            'consistency_score': 0.5
        }
        
        # Weights for different quality aspects
        self.quality_weights = {
            'text_quality': 0.3,
            'structure_quality': 0.25,
            'consistency': 0.2,
            'completeness': 0.15,
            'formatting': 0.1
        }
    
    def assess_table_quality(self, table_data: List[List[str]], 
                           processing_metadata: Optional[Dict] = None) -> Dict[str, Any]:
        """Comprehensive table quality assessment"""
        
        if not table_data:
            return {
                'overall_score': 0.0,
                'quality_level': 'unacceptable',
                'needs_retry': True,
                'issues': ['Empty table data'],
                'recommendations': ['Check image quality and preprocessing'],
                'detailed_scores': {},
                'retry_suggestions': ['Try different OCR engine', 'Improve image preprocessing']
            }
        
        print("  📊 Starting comprehensive quality assessment...")
        
        # Individual quality assessments
        text_quality = self.assess_text_quality(table_data)
        structure_quality = self.assess_structure_quality(table_data)
        consistency_score = self.assess_consistency(table_data)
        completeness_score = self.assess_completeness(table_data)
        formatting_score = self.assess_formatting(table_data)
        
        # Calculate weighted overall score
        overall_score = (
            text_quality['score'] * self.quality_weights['text_quality'] +
            structure_quality['score'] * self.quality_weights['structure_quality'] +
            consistency_score['score'] * self.quality_weights['consistency'] +
            completeness_score['score'] * self.quality_weights['completeness'] +
            formatting_score['score'] * self.quality_weights['formatting']
        )
        
        # Determine quality level
        quality_level = self.get_quality_level(overall_score)
        
        # Determine if retry is needed
        needs_retry = self.should_retry(overall_score, {
            'text_quality': text_quality,
            'structure_quality': structure_quality,
            'consistency': consistency_score,
            'completeness': completeness_score,
            'formatting': formatting_score
        })
        
        # Collect all issues and recommendations
        all_issues = []
        all_recommendations = []
        retry_suggestions = []
        
        for assessment in [text_quality, structure_quality, consistency_score, 
                          completeness_score, formatting_score]:
            all_issues.extend(assessment.get('issues', []))
            all_recommendations.extend(assessment.get('recommendations', []))
            retry_suggestions.extend(assessment.get('retry_suggestions', []))
        
        # Remove duplicates while preserving order
        all_issues = list(dict.fromkeys(all_issues))
        all_recommendations = list(dict.fromkeys(all_recommendations))
        retry_suggestions = list(dict.fromkeys(retry_suggestions))
        
        detailed_scores = {
            'text_quality': text_quality,
            'structure_quality': structure_quality,
            'consistency': consistency_score,
            'completeness': completeness_score,
            'formatting': formatting_score
        }
        
        print(f"    📈 Overall quality score: {overall_score:.2f} ({quality_level})")
        print(f"    🔄 Retry needed: {'Yes' if needs_retry else 'No'}")
        
        return {
            'overall_score': overall_score,
            'quality_level': quality_level,
            'needs_retry': needs_retry,
            'issues': all_issues,
            'recommendations': all_recommendations,
            'detailed_scores': detailed_scores,
            'retry_suggestions': retry_suggestions
        }
    
    def assess_text_quality(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Assess the quality of extracted text"""
        scores = []
        issues = []
        recommendations = []
        retry_suggestions = []
        
        total_cells = 0
        empty_cells = 0
        suspicious_cells = 0
        
        for row in table_data:
            for cell in row:
                total_cells += 1
                
                if not cell or not cell.strip():
                    empty_cells += 1
                    continue
                
                # Check for OCR artifacts
                cell_score = self.score_cell_text_quality(cell)
                scores.append(cell_score)
                
                if cell_score < 0.5:
                    suspicious_cells += 1
        
        if not scores:
            avg_score = 0.0
            issues.append("No readable text found")
            retry_suggestions.append("Try different OCR engine")
        else:
            avg_score = statistics.mean(scores)
        
        # Calculate metrics
        empty_cell_ratio = empty_cells / total_cells if total_cells > 0 else 1.0
        suspicious_cell_ratio = suspicious_cells / total_cells if total_cells > 0 else 0.0
        
        # Adjust score based on metrics
        final_score = avg_score * (1 - empty_cell_ratio * 0.5) * (1 - suspicious_cell_ratio * 0.3)
        
        # Generate issues and recommendations
        if empty_cell_ratio > 0.3:
            issues.append(f"High empty cell ratio: {empty_cell_ratio:.1%}")
            recommendations.append("Check image quality and OCR settings")
        
        if suspicious_cell_ratio > 0.2:
            issues.append(f"Many suspicious text extractions: {suspicious_cell_ratio:.1%}")
            retry_suggestions.append("Try multi-engine OCR approach")
        
        return {
            'score': final_score,
            'issues': issues,
            'recommendations': recommendations,
            'retry_suggestions': retry_suggestions,
            'metrics': {
                'empty_cell_ratio': empty_cell_ratio,
                'suspicious_cell_ratio': suspicious_cell_ratio,
                'average_text_score': avg_score
            }
        }
    
    def score_cell_text_quality(self, text: str) -> float:
        """Score individual cell text quality"""
        if not text or not text.strip():
            return 0.0
        
        score = 1.0
        
        # Check for common OCR errors
        error_patterns = [
            (r'[^\w\s\.\,\-\+\=\(\)\[\]\{\}\$\%\&\#\@\!\?]', -0.2),  # Unusual characters
            (r'\s{3,}', -0.1),  # Multiple spaces
            (r'[a-zA-Z]{15,}', -0.3),  # Very long words (likely OCR errors)
            (r'^[^a-zA-Z0-9]*$', -0.5),  # Only special characters
        ]
        
        for pattern, penalty in error_patterns:
            if re.search(pattern, text):
                score += penalty
        
        # Boost score for well-formatted content
        if re.search(r'[\d\$\£\€\%]', text):  # Contains numbers or currency
            score += 0.1
        
        if len(text.split()) > 1 and len(text) < 100:  # Reasonable length
            score += 0.1
        
        return max(0.0, min(1.0, score))
    
    def assess_structure_quality(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Assess table structure quality"""
        issues = []
        recommendations = []
        retry_suggestions = []
        
        if len(table_data) < 2:
            return {
                'score': 0.0,
                'issues': ['Insufficient rows for table structure'],
                'recommendations': ['Check table detection parameters'],
                'retry_suggestions': ['Try different table detection method']
            }
        
        # Check row consistency
        row_lengths = [len(row) for row in table_data]
        max_cols = max(row_lengths)
        min_cols = min(row_lengths)
        
        structure_score = 1.0
        
        # Penalize inconsistent column counts
        if max_cols != min_cols:
            inconsistency_ratio = (max_cols - min_cols) / max_cols
            structure_score -= inconsistency_ratio * 0.5
            issues.append(f"Inconsistent column count: {min_cols}-{max_cols}")
            recommendations.append("Check table boundary detection")
        
        # Check for reasonable table dimensions
        if max_cols < 2:
            structure_score -= 0.3
            issues.append("Too few columns detected")
            retry_suggestions.append("Adjust table detection sensitivity")
        
        if len(table_data) < 3:
            structure_score -= 0.2
            issues.append("Very few rows detected")
            recommendations.append("Verify complete table is captured")
        
        # Check for empty rows
        empty_rows = sum(1 for row in table_data if all(not cell.strip() for cell in row))
        if empty_rows > 0:
            empty_ratio = empty_rows / len(table_data)
            structure_score -= empty_ratio * 0.3
            issues.append(f"Empty rows detected: {empty_rows}")
        
        return {
            'score': max(0.0, structure_score),
            'issues': issues,
            'recommendations': recommendations,
            'retry_suggestions': retry_suggestions,
            'metrics': {
                'row_count': len(table_data),
                'column_range': (min_cols, max_cols),
                'empty_rows': empty_rows
            }
        }
    
    def assess_consistency(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Assess data consistency within the table"""
        issues = []
        recommendations = []
        retry_suggestions = []
        
        if not table_data or len(table_data) < 2:
            return {'score': 0.0, 'issues': ['Insufficient data for consistency check']}
        
        consistency_score = 1.0
        
        # Check column type consistency
        max_cols = max(len(row) for row in table_data)
        
        for col_idx in range(max_cols):
            column_data = [row[col_idx] if col_idx < len(row) else "" for row in table_data[1:]]  # Skip header
            column_consistency = self.assess_column_consistency(column_data, col_idx)
            
            if column_consistency['score'] < 0.7:
                consistency_score -= 0.1
                issues.extend(column_consistency['issues'])
        
        # Check for duplicate rows
        row_strings = [str(row) for row in table_data]
        unique_rows = len(set(row_strings))
        if unique_rows < len(table_data):
            duplicate_ratio = (len(table_data) - unique_rows) / len(table_data)
            consistency_score -= duplicate_ratio * 0.3
            issues.append(f"Duplicate rows detected: {len(table_data) - unique_rows}")
        
        return {
            'score': max(0.0, consistency_score),
            'issues': issues,
            'recommendations': recommendations,
            'retry_suggestions': retry_suggestions
        }
    
    def assess_column_consistency(self, column_data: List[str], col_idx: int) -> Dict[str, Any]:
        """Assess consistency within a single column"""
        if not column_data:
            return {'score': 1.0, 'issues': []}
        
        # Determine expected data type
        numeric_count = sum(1 for cell in column_data if self.is_numeric(cell))
        text_count = len(column_data) - numeric_count
        
        consistency_score = 1.0
        issues = []
        
        # If column should be numeric but has mixed types
        if numeric_count > text_count and text_count > 0:
            mixed_ratio = text_count / len(column_data)
            consistency_score -= mixed_ratio * 0.5
            issues.append(f"Column {col_idx+1} has mixed numeric/text data")
        
        return {'score': consistency_score, 'issues': issues}
    
    def is_numeric(self, text: str) -> bool:
        """Check if text represents a numeric value"""
        if not text or not isinstance(text, str):
            return False
        
        # Remove common formatting
        clean_text = text.replace(',', '').replace('$', '').replace('(', '').replace(')', '').strip()
        
        try:
            float(clean_text)
            return True
        except ValueError:
            return False
    
    def assess_completeness(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Assess table completeness"""
        if not table_data:
            return {'score': 0.0, 'issues': ['No data']}
        
        total_cells = sum(len(row) for row in table_data)
        filled_cells = sum(1 for row in table_data for cell in row if cell and cell.strip())
        
        completeness_ratio = filled_cells / total_cells if total_cells > 0 else 0.0
        
        issues = []
        if completeness_ratio < 0.7:
            issues.append(f"Low data completeness: {completeness_ratio:.1%}")
        
        return {
            'score': completeness_ratio,
            'issues': issues,
            'recommendations': [],
            'retry_suggestions': []
        }
    
    def assess_formatting(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Assess formatting quality"""
        formatting_score = 1.0
        issues = []
        
        # Check for consistent formatting patterns
        for row in table_data:
            for cell in row:
                if cell and len(cell) > 50:  # Very long cells might indicate formatting issues
                    formatting_score -= 0.05
                    if len(issues) < 3:  # Limit issue reporting
                        issues.append("Some cells contain excessive text")
        
        return {
            'score': max(0.0, formatting_score),
            'issues': issues,
            'recommendations': [],
            'retry_suggestions': []
        }
    
    def get_quality_level(self, score: float) -> str:
        """Convert numeric score to quality level"""
        for level, threshold in self.quality_thresholds.items():
            if score >= threshold:
                return level
        return 'unacceptable'
    
    def should_retry(self, overall_score: float, detailed_scores: Dict[str, Any]) -> bool:
        """Determine if processing should be retried"""
        
        # Check overall score threshold
        if overall_score < self.retry_thresholds['overall_quality']:
            return True
        
        # Check individual component thresholds
        if detailed_scores['text_quality']['score'] < self.retry_thresholds['text_confidence']:
            return True
        
        if detailed_scores['structure_quality']['score'] < self.retry_thresholds['structure_confidence']:
            return True
        
        if detailed_scores['consistency']['score'] < self.retry_thresholds['consistency_score']:
            return True
        
        return False
    
    def suggest_retry_strategy(self, quality_assessment: Dict[str, Any]) -> List[str]:
        """Suggest specific retry strategies based on quality assessment"""
        strategies = []
        
        detailed_scores = quality_assessment.get('detailed_scores', {})
        
        # Text quality issues
        if detailed_scores.get('text_quality', {}).get('score', 1.0) < 0.5:
            strategies.extend([
                "Try different OCR engine (PaddleOCR or Tesseract)",
                "Improve image preprocessing (contrast, sharpness)",
                "Use multi-engine OCR with voting"
            ])
        
        # Structure quality issues
        if detailed_scores.get('structure_quality', {}).get('score', 1.0) < 0.5:
            strategies.extend([
                "Adjust table detection parameters",
                "Try Camelot PDF extraction if available",
                "Manual table boundary adjustment"
            ])
        
        # Consistency issues
        if detailed_scores.get('consistency', {}).get('score', 1.0) < 0.5:
            strategies.extend([
                "Apply post-processing validation",
                "Use context-aware correction",
                "Manual review of suspicious cells"
            ])
        
        return list(dict.fromkeys(strategies))  # Remove duplicates

# Global instance
quality_assessor = QualityAssessmentSystem()
