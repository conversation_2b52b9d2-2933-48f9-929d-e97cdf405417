"""
Test script to demonstrate LaTeX text processing improvements
This shows how the bad LaTeX OCR results are now cleaned up
"""

from latex_text_processor import clean_latex_for_word

def test_latex_processing():
    """Test the LaTeX text processor with examples from the screenshot"""
    
    print("🧹 LaTeX Text Processing Test")
    print("=" * 60)
    print("This demonstrates how complex LaTeX expressions are converted")
    print("to readable text for Word documents.")
    print()
    
    # Examples of bad LaTeX OCR results that need cleaning
    bad_latex_examples = [
        # Financial statement headers
        r'\text{Statement of financial position at 1 January 2018}',
        r'\text{Assets}',
        r'\text{Non-Current Assets}',
        r'\text{Current Assets}',
        r'\text{Equity and Liabilities}',
        
        # Asset items with calculations
        r'\text{Tangible (\$564 000 + \$148 000)}',
        r'\text{Intangible - Goodwill (W 1)}',
        r'\text{Inventory (\$62 000 + \$41 000)}',
        r'\text{Trade receivables (\$83 700 + \$17 000)}',
        r'\text{Bank and cash equivalents (\$38 400 + (\$15 000 W 2))}',
        
        # Equity and liability items
        r'\text{Ordinary shares of \$1 each (\$700 000 + (50 000 × \$1))}',
        r'\text{Share premium (50 000 × \$1.5)}',
        r'\text{Retained earnings}',
        r'\text{Trade payables (\$73 600 + \$30 000)}',
        
        # Mathematical expressions
        r'\overline{1 173 100}',  # Overlined totals
        r'\frac{790 000}{135 000}',  # Fractions
        
        # Common OCR errors
        r'790 OOO',  # O's instead of zeros
        r'S564',     # S instead of dollar sign
        r'34 OOO',   # More O's
        r'836 OOO',  # More O's
        r'G000dwill', # Goodwill OCR error
    ]
    
    print("BEFORE (Bad LaTeX OCR) -> AFTER (Clean Text)")
    print("-" * 60)
    
    for i, latex_text in enumerate(bad_latex_examples, 1):
        cleaned_text = clean_latex_for_word(latex_text, 'latex')
        
        # Show the improvement
        print(f"{i:2d}. {latex_text}")
        print(f"    -> {cleaned_text}")
        print()
    
    print("✅ LaTeX text processing complete!")
    print()
    print("Key improvements:")
    print("• Removes LaTeX commands like \\text{}, \\overline{}, \\frac{}")
    print("• Converts mathematical expressions to readable format")
    print("• Fixes common OCR errors (O -> 0, S -> $)")
    print("• Standardizes financial terminology")
    print("• Maintains proper spacing and formatting")

def test_normal_ocr_processing():
    """Test normal OCR text processing"""
    
    print("\n" + "🔧 Normal OCR Text Processing Test")
    print("=" * 60)
    print("This shows how normal OCR results are also improved.")
    print()
    
    normal_ocr_examples = [
        "790 OOO",  # Common OCR error
        "S564 000",  # Dollar sign error
        "G000dwill",  # Goodwill error
        "Non - Current Assets",  # Spacing issue
        "Trade receivables",  # Capitalization
        "retained earnings",  # Capitalization
    ]
    
    print("BEFORE (Normal OCR) -> AFTER (Cleaned)")
    print("-" * 40)
    
    for i, ocr_text in enumerate(normal_ocr_examples, 1):
        cleaned_text = clean_latex_for_word(ocr_text, 'normal')
        
        print(f"{i}. {ocr_text}")
        print(f"   -> {cleaned_text}")
        print()
    
    print("✅ Normal OCR text processing complete!")

if __name__ == "__main__":
    test_latex_processing()
    test_normal_ocr_processing()
    
    print("\n" + "🎯 Summary")
    print("=" * 60)
    print("The LaTeX text processor has been integrated into both:")
    print("• main.py (command-line version)")
    print("• yark_tabular_extraction_gui.py (GUI version)")
    print()
    print("This will automatically clean up OCR results and produce")
    print("much more readable text in your Word documents!")
