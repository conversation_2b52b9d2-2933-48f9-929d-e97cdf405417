"""
Intelligent Post-Processing System for Yark Tabular Extraction
Provides context-aware validation, correction, and enhancement of OCR results
"""

import re
import difflib
from typing import List, Dict, Tuple, Optional, Any
from decimal import Decimal, InvalidOperation
import statistics

class IntelligentPostProcessor:
    """Advanced post-processing with context awareness and intelligent corrections"""
    
    def __init__(self):
        # Financial terms dictionary for spell checking
        self.financial_dictionary = {
            'assets', 'liabilities', 'equity', 'capital', 'revenue', 'expenses', 'profit', 'loss',
            'cash', 'inventory', 'receivables', 'payables', 'depreciation', 'goodwill', 'retained',
            'earnings', 'dividends', 'shares', 'bonds', 'investments', 'loans', 'mortgage',
            'interest', 'tax', 'balance', 'sheet', 'statement', 'income', 'flow', 'budget',
            'account', 'debit', 'credit', 'journal', 'ledger', 'trial', 'partnership', 'corporation'
        }
        
        # Common OCR error patterns and corrections
        self.ocr_corrections = {
            # Character substitutions
            'O': '0',  # In numeric contexts
            'l': '1',  # In numeric contexts
            'I': '1',  # In numeric contexts
            'S': '5',  # In numeric contexts
            'G': '6',  # In numeric contexts
            'B': '8',  # In numeric contexts
            
            # Word corrections
            'Assests': 'Assets',
            'Liabilites': 'Liabilities',
            'Liabilitles': 'Liabilities',
            'Equty': 'Equity',
            'Reveue': 'Revenue',
            'Revenu': 'Revenue',
            'Expeses': 'Expenses',
            'Expens': 'Expenses',
            'Proft': 'Profit',
            'Proflt': 'Profit',
            'Balanc': 'Balance',
            'Balace': 'Balance',
            'Captial': 'Capital',
            'Captal': 'Capital',
            'Goodwil': 'Goodwill',
            'Goodwill': 'Goodwill',
            'Retaind': 'Retained',
            'Retaine': 'Retained'
        }
        
        # Contextual validation patterns
        self.validation_patterns = {
            'currency': r'[\$£€¥₹]\s*[\d,]+\.?\d*',
            'percentage': r'\d+\.?\d*\s*%',
            'date': r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}',
            'account_number': r'\d{3,}-?\d{3,}',
            'decimal_number': r'\d{1,3}(,\d{3})*\.?\d*',
            'negative_parentheses': r'\(\s*[\d,]+\.?\d*\s*\)'
        }
        
        # Context-specific validation rules
        self.context_rules = {
            'balance_sheet': {
                'required_sections': ['assets', 'liabilities', 'equity'],
                'balance_equation': 'assets = liabilities + equity',
                'typical_accounts': ['cash', 'inventory', 'receivables', 'payables', 'capital']
            },
            'income_statement': {
                'required_sections': ['revenue', 'expenses'],
                'calculation_flow': 'revenue - expenses = profit/loss',
                'typical_accounts': ['sales', 'cost of goods sold', 'operating expenses', 'net income']
            },
            'cash_flow': {
                'required_sections': ['operating', 'investing', 'financing'],
                'typical_accounts': ['cash receipts', 'cash payments', 'net cash flow']
            }
        }
    
    def validate_and_correct_text(self, text: str, context: str = "general") -> Tuple[str, float, List[str]]:
        """Main validation and correction function"""
        if not text or not isinstance(text, str):
            return text, 0.0, []
        
        original_text = text
        corrections_made = []
        confidence_score = 1.0
        
        # Step 1: Basic text cleaning
        text = self.clean_basic_text(text)
        if text != original_text:
            corrections_made.append("basic_cleaning")
        
        # Step 2: OCR error correction
        corrected_text, ocr_corrections = self.correct_ocr_errors(text, context)
        if ocr_corrections:
            text = corrected_text
            corrections_made.extend(ocr_corrections)
            confidence_score += 0.1  # Boost confidence for corrections
        
        # Step 3: Spell checking for financial terms
        spell_corrected, spell_corrections = self.spell_check_financial_terms(text)
        if spell_corrections:
            text = spell_corrected
            corrections_made.extend(spell_corrections)
            confidence_score += 0.05
        
        # Step 4: Format validation and correction
        format_corrected, format_corrections = self.validate_and_correct_formats(text, context)
        if format_corrections:
            text = format_corrected
            corrections_made.extend(format_corrections)
            confidence_score += 0.05
        
        # Step 5: Context-specific validation
        context_score = self.validate_context_appropriateness(text, context)
        confidence_score *= context_score
        
        # Final confidence adjustment
        confidence_score = min(0.95, max(0.1, confidence_score))
        
        return text, confidence_score, corrections_made
    
    def clean_basic_text(self, text: str) -> str:
        """Basic text cleaning operations"""
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Remove common OCR artifacts
        text = re.sub(r'[^\w\s\.\,\-\+\=\(\)\[\]\{\}\$\%\&\#\@\!\?]', '', text)
        
        # Fix multiple punctuation
        text = re.sub(r'\.{2,}', '.', text)
        text = re.sub(r',{2,}', ',', text)
        
        return text.strip()
    
    def correct_ocr_errors(self, text: str, context: str) -> Tuple[str, List[str]]:
        """Correct common OCR errors based on context"""
        corrections = []
        corrected_text = text
        
        # Numeric context corrections
        if self.is_numeric_context(text):
            for error, correction in self.ocr_corrections.items():
                if error in ['O', 'l', 'I', 'S', 'G', 'B']:
                    pattern = f'(?<=\\d){error}(?=\\d)|^{error}(?=\\d)|(?<=\\d){error}$'
                    if re.search(pattern, corrected_text):
                        corrected_text = re.sub(pattern, correction, corrected_text)
                        corrections.append(f"numeric_{error}_to_{correction}")
        
        # Word-level corrections
        for error, correction in self.ocr_corrections.items():
            if len(error) > 1 and error.lower() in corrected_text.lower():
                # Case-insensitive replacement preserving original case
                pattern = re.compile(re.escape(error), re.IGNORECASE)
                if pattern.search(corrected_text):
                    corrected_text = pattern.sub(correction, corrected_text)
                    corrections.append(f"word_{error}_to_{correction}")
        
        return corrected_text, corrections
    
    def is_numeric_context(self, text: str) -> bool:
        """Determine if text is in a numeric context"""
        # Check if text contains numbers or currency symbols
        return bool(re.search(r'[\d\$£€¥₹%]', text))
    
    def spell_check_financial_terms(self, text: str) -> Tuple[str, List[str]]:
        """Spell check against financial dictionary"""
        corrections = []
        words = text.split()
        corrected_words = []
        
        for word in words:
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            if len(clean_word) > 3:  # Only check words longer than 3 characters
                # Find closest match in financial dictionary
                closest_matches = difflib.get_close_matches(
                    clean_word, self.financial_dictionary, n=1, cutoff=0.8
                )
                
                if closest_matches and closest_matches[0] != clean_word:
                    # Preserve original case pattern
                    corrected_word = self.preserve_case_pattern(word, closest_matches[0])
                    corrected_words.append(corrected_word)
                    corrections.append(f"spell_{word}_to_{corrected_word}")
                else:
                    corrected_words.append(word)
            else:
                corrected_words.append(word)
        
        return ' '.join(corrected_words), corrections
    
    def preserve_case_pattern(self, original: str, correction: str) -> str:
        """Preserve the case pattern of the original word"""
        if original.isupper():
            return correction.upper()
        elif original.islower():
            return correction.lower()
        elif original.istitle():
            return correction.title()
        else:
            return correction
    
    def validate_and_correct_formats(self, text: str, context: str) -> Tuple[str, List[str]]:
        """Validate and correct number formats, currencies, etc."""
        corrections = []
        corrected_text = text
        
        # Currency format correction
        currency_matches = re.finditer(r'[\$£€¥₹]\s*[\d,]+\.?\d*', corrected_text)
        for match in currency_matches:
            original = match.group()
            corrected = self.format_currency(original)
            if corrected != original:
                corrected_text = corrected_text.replace(original, corrected)
                corrections.append(f"currency_format_{original}_to_{corrected}")
        
        # Negative number format (parentheses)
        paren_matches = re.finditer(r'\(\s*([\d,]+\.?\d*)\s*\)', corrected_text)
        for match in paren_matches:
            original = match.group()
            number = match.group(1)
            corrected = f"({number})"  # Standardize spacing
            if corrected != original:
                corrected_text = corrected_text.replace(original, corrected)
                corrections.append(f"negative_format_{original}_to_{corrected}")
        
        # Decimal number format
        decimal_matches = re.finditer(r'\d{1,3}(,\d{3})*\.?\d*', corrected_text)
        for match in decimal_matches:
            original = match.group()
            corrected = self.format_decimal_number(original)
            if corrected != original:
                corrected_text = corrected_text.replace(original, corrected)
                corrections.append(f"decimal_format_{original}_to_{corrected}")
        
        return corrected_text, corrections
    
    def format_currency(self, currency_text: str) -> str:
        """Format currency text properly"""
        # Extract currency symbol and number
        match = re.match(r'([\$£€¥₹])\s*([\d,]+\.?\d*)', currency_text)
        if match:
            symbol, number = match.groups()
            # Remove extra spaces and format consistently
            return f"{symbol}{number}"
        return currency_text
    
    def format_decimal_number(self, number_text: str) -> str:
        """Format decimal numbers with proper comma separation"""
        try:
            # Remove existing commas and convert to float
            clean_number = number_text.replace(',', '')
            if '.' in clean_number:
                integer_part, decimal_part = clean_number.split('.')
                # Add commas to integer part
                formatted_integer = f"{int(integer_part):,}"
                return f"{formatted_integer}.{decimal_part}"
            else:
                # Integer number
                return f"{int(clean_number):,}"
        except ValueError:
            return number_text
    
    def validate_context_appropriateness(self, text: str, context: str) -> float:
        """Validate if text is appropriate for the given context"""
        if context not in self.context_rules:
            return 1.0  # No specific rules, assume valid
        
        rules = self.context_rules[context]
        score = 1.0
        
        # Check for required sections (for headers/labels)
        text_lower = text.lower()
        if 'required_sections' in rules:
            section_found = any(section in text_lower for section in rules['required_sections'])
            if len(text) > 10 and not section_found:  # Only penalize longer text
                score *= 0.9
        
        # Check for typical accounts
        if 'typical_accounts' in rules:
            account_found = any(account in text_lower for account in rules['typical_accounts'])
            if account_found:
                score *= 1.1  # Boost confidence for recognized accounts
        
        return min(1.0, score)
    
    def validate_table_consistency(self, table_data: List[List[str]], table_type: str) -> Dict[str, Any]:
        """Validate overall table consistency and suggest corrections"""
        validation_result = {
            'is_consistent': True,
            'issues': [],
            'suggestions': [],
            'confidence_score': 1.0,
            'corrected_cells': []
        }
        
        if not table_data:
            validation_result['is_consistent'] = False
            validation_result['issues'].append("Empty table data")
            return validation_result
        
        # Check row consistency
        row_lengths = [len(row) for row in table_data]
        if len(set(row_lengths)) > 1:
            validation_result['issues'].append("Inconsistent row lengths")
            validation_result['confidence_score'] *= 0.8
        
        # Validate numeric columns
        numeric_columns = self.identify_numeric_columns(table_data)
        for col_idx in numeric_columns:
            column_data = [row[col_idx] if col_idx < len(row) else "" for row in table_data]
            column_issues = self.validate_numeric_column(column_data, col_idx)
            validation_result['issues'].extend(column_issues)
        
        # Context-specific validation
        if table_type in self.context_rules:
            context_validation = self.validate_table_context(table_data, table_type)
            validation_result['issues'].extend(context_validation['issues'])
            validation_result['suggestions'].extend(context_validation['suggestions'])
        
        # Calculate overall confidence
        issue_count = len(validation_result['issues'])
        if issue_count > 0:
            validation_result['confidence_score'] *= max(0.3, 1.0 - (issue_count * 0.1))
        
        validation_result['is_consistent'] = validation_result['confidence_score'] > 0.7
        
        return validation_result
    
    def identify_numeric_columns(self, table_data: List[List[str]]) -> List[int]:
        """Identify which columns contain primarily numeric data"""
        if not table_data:
            return []
        
        max_cols = max(len(row) for row in table_data)
        numeric_columns = []
        
        for col_idx in range(max_cols):
            column_data = [row[col_idx] if col_idx < len(row) else "" for row in table_data[1:]]  # Skip header
            numeric_count = sum(1 for cell in column_data if self.is_numeric_value(cell))
            
            # Consider column numeric if >70% of cells are numeric
            if len(column_data) > 0 and numeric_count / len(column_data) > 0.7:
                numeric_columns.append(col_idx)
        
        return numeric_columns
    
    def is_numeric_value(self, text: str) -> bool:
        """Check if text represents a numeric value"""
        if not text or not isinstance(text, str):
            return False
        
        # Remove common formatting
        clean_text = text.replace(',', '').replace('$', '').replace('(', '').replace(')', '').strip()
        
        try:
            float(clean_text)
            return True
        except ValueError:
            return False
    
    def validate_numeric_column(self, column_data: List[str], col_idx: int) -> List[str]:
        """Validate numeric column for consistency and errors"""
        issues = []
        
        numeric_values = []
        for i, cell in enumerate(column_data):
            if self.is_numeric_value(cell):
                try:
                    value = float(cell.replace(',', '').replace('$', '').replace('(', '').replace(')', ''))
                    numeric_values.append(value)
                except ValueError:
                    issues.append(f"Invalid numeric format in column {col_idx}, row {i+1}: '{cell}'")
        
        if numeric_values:
            # Check for outliers
            if len(numeric_values) > 3:
                median_val = statistics.median(numeric_values)
                for i, value in enumerate(numeric_values):
                    if abs(value) > median_val * 10:  # Potential outlier
                        issues.append(f"Potential outlier in column {col_idx}, row {i+1}: {value}")
        
        return issues
    
    def validate_table_context(self, table_data: List[List[str]], table_type: str) -> Dict[str, Any]:
        """Validate table against context-specific rules"""
        result = {'issues': [], 'suggestions': []}
        
        if table_type == 'balance_sheet':
            # Check for balance sheet specific validation
            all_text = ' '.join([' '.join(row) for row in table_data]).lower()
            
            required_sections = ['assets', 'liabilities']
            missing_sections = [section for section in required_sections if section not in all_text]
            
            if missing_sections:
                result['issues'].append(f"Missing balance sheet sections: {', '.join(missing_sections)}")
                result['suggestions'].append("Verify table contains all required balance sheet sections")
        
        elif table_type == 'income_statement':
            all_text = ' '.join([' '.join(row) for row in table_data]).lower()
            
            if 'revenue' not in all_text and 'sales' not in all_text:
                result['issues'].append("No revenue/sales line found in income statement")
            
            if 'expenses' not in all_text and 'cost' not in all_text:
                result['issues'].append("No expenses/costs line found in income statement")
        
        return result
    
    def post_process_table(self, table_data: List[List[str]], table_type: str = "general") -> Tuple[List[List[str]], Dict[str, Any]]:
        """Main post-processing function for entire table"""
        if not table_data:
            return table_data, {'processed': False, 'reason': 'Empty table'}
        
        print("  🧠 Starting intelligent post-processing...")
        
        processed_table = []
        processing_stats = {
            'total_cells': 0,
            'cells_corrected': 0,
            'corrections_made': [],
            'confidence_scores': [],
            'validation_result': {}
        }
        
        # Process each cell
        for row_idx, row in enumerate(table_data):
            processed_row = []
            
            for col_idx, cell in enumerate(row):
                processing_stats['total_cells'] += 1
                
                # Determine context for this cell
                cell_context = self.determine_cell_context(row_idx, col_idx, table_type)
                
                # Process the cell
                corrected_text, confidence, corrections = self.validate_and_correct_text(cell, cell_context)
                
                processed_row.append(corrected_text)
                processing_stats['confidence_scores'].append(confidence)
                
                if corrections:
                    processing_stats['cells_corrected'] += 1
                    processing_stats['corrections_made'].extend(corrections)
            
            processed_table.append(processed_row)
        
        # Validate overall table consistency
        validation_result = self.validate_table_consistency(processed_table, table_type)
        processing_stats['validation_result'] = validation_result
        
        # Calculate overall statistics
        avg_confidence = sum(processing_stats['confidence_scores']) / len(processing_stats['confidence_scores'])
        correction_rate = processing_stats['cells_corrected'] / processing_stats['total_cells']
        
        print(f"    📊 Processed {processing_stats['total_cells']} cells")
        print(f"    🔧 Corrected {processing_stats['cells_corrected']} cells ({correction_rate:.1%})")
        print(f"    📈 Average confidence: {avg_confidence:.2f}")
        print(f"    ✅ Table consistency: {'PASS' if validation_result['is_consistent'] else 'ISSUES'}")
        
        processing_stats.update({
            'average_confidence': avg_confidence,
            'correction_rate': correction_rate,
            'processed': True
        })
        
        return processed_table, processing_stats
    
    def determine_cell_context(self, row_idx: int, col_idx: int, table_type: str) -> str:
        """Determine the context of a specific cell for targeted processing"""
        # First row is usually headers
        if row_idx == 0:
            return "header"
        
        # First column is usually labels/descriptions
        if col_idx == 0:
            return "label"
        
        # Other columns are usually data
        if table_type in ["balance_sheet", "income_statement", "financial_general"]:
            return "financial_data"
        elif table_type == "mathematical":
            return "mathematical"
        else:
            return "general"

# Global instance
intelligent_postprocessor = IntelligentPostProcessor()
