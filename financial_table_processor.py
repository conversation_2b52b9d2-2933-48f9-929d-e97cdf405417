"""
Specialized Financial Table Processing for Yark Tabular Extraction
Handles financial statements, balance sheets, and accounting-specific layouts
"""

import re
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from decimal import Decimal, InvalidOperation

class FinancialTableProcessor:
    """Advanced processor for financial tables with accounting-specific logic"""
    
    def __init__(self):
        self.currency_symbols = ['$', '£', '€', '¥', '₹', '₽', '₩', '₪', '₦', '₡']
        self.financial_keywords = {
            'assets': ['assets', 'asset', 'current assets', 'non-current assets', 'fixed assets'],
            'liabilities': ['liabilities', 'liability', 'current liabilities', 'long-term liabilities'],
            'equity': ['equity', 'shareholders equity', 'owners equity', 'capital', 'retained earnings'],
            'income': ['revenue', 'income', 'sales', 'turnover', 'gross profit', 'net profit'],
            'expenses': ['expenses', 'cost', 'operating expenses', 'administrative expenses'],
            'cash_flow': ['cash flow', 'operating cash flow', 'investing cash flow', 'financing cash flow']
        }
        
        # Common accounting abbreviations and their full forms
        self.accounting_abbreviations = {
            'b/f': 'brought forward',
            'c/f': 'carried forward', 
            'b/d': 'brought down',
            'c/d': 'carried down',
            'dr': 'debit',
            'cr': 'credit',
            'a/c': 'account',
            'p&l': 'profit and loss',
            'bal': 'balance',
            'dep': 'depreciation',
            'acc': 'accumulated'
        }
        
    def detect_financial_table_type(self, table_data: List[List[str]]) -> str:
        """Detect the specific type of financial table"""
        if not table_data:
            return "unknown"
        
        # Convert all table content to lowercase for analysis
        all_text = ""
        for row in table_data:
            for cell in row:
                if isinstance(cell, str):
                    all_text += " " + cell.lower()
        
        # Balance Sheet indicators
        balance_sheet_indicators = ['assets', 'liabilities', 'equity', 'balance sheet', 'financial position']
        if any(indicator in all_text for indicator in balance_sheet_indicators):
            # Check for specific balance sheet patterns
            if 'current assets' in all_text and 'current liabilities' in all_text:
                return "balance_sheet_classified"
            elif 'total assets' in all_text:
                return "balance_sheet_simple"
            else:
                return "balance_sheet_general"
        
        # Income Statement indicators
        income_indicators = ['revenue', 'sales', 'gross profit', 'net profit', 'income statement', 'p&l']
        if any(indicator in all_text for indicator in income_indicators):
            return "income_statement"
        
        # Cash Flow Statement indicators
        cash_flow_indicators = ['cash flow', 'operating activities', 'investing activities', 'financing activities']
        if any(indicator in all_text for indicator in cash_flow_indicators):
            return "cash_flow_statement"
        
        # Partnership/Capital Account indicators
        partnership_indicators = ['capital account', 'partner', 'goodwill', 'drawings', 'profit sharing']
        if any(indicator in all_text for indicator in partnership_indicators):
            return "partnership_capital"
        
        # Trial Balance indicators
        trial_balance_indicators = ['trial balance', 'debit', 'credit', 'dr', 'cr']
        if sum(1 for indicator in trial_balance_indicators if indicator in all_text) >= 2:
            return "trial_balance"
        
        # General financial table
        financial_indicators = ['$', '£', '€', 'total', 'amount', 'balance']
        if any(indicator in all_text for indicator in financial_indicators):
            return "financial_general"
        
        return "unknown"
    
    def clean_financial_text(self, text: str) -> str:
        """Clean and standardize financial text"""
        if not text or not isinstance(text, str):
            return ""
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        # Expand common accounting abbreviations
        text_lower = text.lower()
        for abbrev, full_form in self.accounting_abbreviations.items():
            text_lower = text_lower.replace(abbrev, full_form)
        
        # Preserve original case for the result but use cleaned version
        # This is a simple approach - in practice, you might want more sophisticated handling
        
        # Fix common OCR errors in financial terms
        financial_corrections = {
            'Goad': 'Good',
            'will': 'will',  # This might be 'goodwill' context-dependent
            'Assests': 'Assets',
            'Liabilites': 'Liabilities',
            'Equty': 'Equity',
            'Reveue': 'Revenue',
            'Expeses': 'Expenses'
        }
        
        for error, correction in financial_corrections.items():
            text = text.replace(error, correction)
        
        return text.strip()
    
    def detect_and_format_currency(self, text: str) -> Tuple[str, Optional[str]]:
        """Detect currency and format financial amounts"""
        if not text or not isinstance(text, str):
            return text, None
        
        # Detect currency symbol
        detected_currency = None
        for symbol in self.currency_symbols:
            if symbol in text:
                detected_currency = symbol
                break
        
        # Extract and format numbers
        # Pattern to match various number formats: 1,234.56, (1,234.56), 1234.56, etc.
        number_patterns = [
            r'\(([0-9,]+\.?[0-9]*)\)',  # Parentheses (negative)
            r'([0-9,]+\.?[0-9]*)',      # Regular numbers
        ]
        
        formatted_text = text
        for pattern in number_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                original = match.group(0)
                number_str = match.group(1)
                
                try:
                    # Remove commas and convert to float
                    clean_number = number_str.replace(',', '')
                    number_value = float(clean_number)
                    
                    # Format based on context
                    if '(' in original:  # Negative number in parentheses
                        if detected_currency:
                            formatted_number = f"({detected_currency}{number_value:,.2f})"
                        else:
                            formatted_number = f"({number_value:,.2f})"
                    else:  # Positive number
                        if detected_currency:
                            formatted_number = f"{detected_currency}{number_value:,.2f}"
                        else:
                            formatted_number = f"{number_value:,.2f}"
                    
                    formatted_text = formatted_text.replace(original, formatted_number)
                    
                except (ValueError, InvalidOperation):
                    # If conversion fails, keep original text
                    continue
        
        return formatted_text, detected_currency
    
    def validate_financial_table(self, table_data: List[List[str]], table_type: str) -> Dict[str, Any]:
        """Validate financial table structure and content"""
        validation_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'confidence': 1.0
        }
        
        if not table_data:
            validation_result['is_valid'] = False
            validation_result['issues'].append("Empty table data")
            return validation_result
        
        # Check for consistent column structure
        row_lengths = [len(row) for row in table_data]
        if len(set(row_lengths)) > 1:
            validation_result['issues'].append("Inconsistent column count across rows")
            validation_result['confidence'] -= 0.2
        
        # Validate based on table type
        if table_type == "balance_sheet_classified":
            validation_result.update(self._validate_balance_sheet(table_data))
        elif table_type == "income_statement":
            validation_result.update(self._validate_income_statement(table_data))
        elif table_type == "partnership_capital":
            validation_result.update(self._validate_partnership_capital(table_data))
        elif table_type == "trial_balance":
            validation_result.update(self._validate_trial_balance(table_data))
        
        return validation_result
    
    def _validate_balance_sheet(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Validate balance sheet specific structure"""
        result = {'issues': [], 'suggestions': []}
        
        # Check for required sections
        all_text = ' '.join([' '.join(row) for row in table_data]).lower()
        
        required_sections = ['assets', 'liabilities']
        missing_sections = [section for section in required_sections if section not in all_text]
        
        if missing_sections:
            result['issues'].append(f"Missing required sections: {', '.join(missing_sections)}")
            result['suggestions'].append("Verify table contains all balance sheet sections")
        
        # Check for balancing (Assets = Liabilities + Equity)
        # This is a simplified check - in practice, you'd need more sophisticated parsing
        if 'total assets' in all_text and 'total liabilities' in all_text:
            result['suggestions'].append("Verify that Assets = Liabilities + Equity")
        
        return result
    
    def _validate_income_statement(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Validate income statement specific structure"""
        result = {'issues': [], 'suggestions': []}
        
        all_text = ' '.join([' '.join(row) for row in table_data]).lower()
        
        # Check for typical income statement items
        expected_items = ['revenue', 'expenses', 'profit']
        missing_items = [item for item in expected_items if item not in all_text]
        
        if len(missing_items) > 1:
            result['issues'].append("May be missing key income statement components")
            result['suggestions'].append("Verify table contains revenue, expenses, and profit calculations")
        
        return result
    
    def _validate_partnership_capital(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Validate partnership capital account structure"""
        result = {'issues': [], 'suggestions': []}
        
        # Check for partner names and capital movements
        all_text = ' '.join([' '.join(row) for row in table_data]).lower()
        
        partnership_indicators = ['capital', 'drawings', 'profit', 'goodwill']
        found_indicators = [indicator for indicator in partnership_indicators if indicator in all_text]
        
        if len(found_indicators) < 2:
            result['issues'].append("Limited partnership account indicators found")
            result['suggestions'].append("Verify table contains capital movements and partner details")
        
        return result
    
    def _validate_trial_balance(self, table_data: List[List[str]]) -> Dict[str, Any]:
        """Validate trial balance structure"""
        result = {'issues': [], 'suggestions': []}
        
        all_text = ' '.join([' '.join(row) for row in table_data]).lower()
        
        # Check for debit and credit columns
        if 'debit' not in all_text and 'dr' not in all_text:
            result['issues'].append("Missing debit column indicator")
        
        if 'credit' not in all_text and 'cr' not in all_text:
            result['issues'].append("Missing credit column indicator")
        
        # Check for account names
        if len(table_data) < 3:  # Header + at least 2 accounts
            result['issues'].append("Insufficient number of accounts for trial balance")
        
        return result
    
    def enhance_financial_table(self, table_data: List[List[str]], table_type: str) -> List[List[str]]:
        """Enhance financial table with formatting and corrections"""
        if not table_data:
            return table_data
        
        enhanced_table = []
        detected_currency = None
        
        for row_idx, row in enumerate(table_data):
            enhanced_row = []
            
            for col_idx, cell in enumerate(row):
                if not cell or not isinstance(cell, str):
                    enhanced_row.append(cell)
                    continue
                
                # Clean the text
                cleaned_text = self.clean_financial_text(cell)
                
                # Format currency if it's a numeric column (typically not the first column)
                if col_idx > 0:
                    formatted_text, currency = self.detect_and_format_currency(cleaned_text)
                    if currency and not detected_currency:
                        detected_currency = currency
                    enhanced_row.append(formatted_text)
                else:
                    # First column is usually account names/descriptions
                    enhanced_row.append(cleaned_text)
            
            enhanced_table.append(enhanced_row)
        
        return enhanced_table
    
    def calculate_financial_totals(self, table_data: List[List[str]], table_type: str) -> Dict[str, Any]:
        """Calculate and verify financial totals"""
        totals = {
            'calculated_totals': {},
            'verification_status': {},
            'discrepancies': []
        }
        
        if not table_data:
            return totals
        
        # Extract numeric values from the table
        numeric_data = []
        for row in table_data:
            numeric_row = []
            for cell in row:
                if isinstance(cell, str):
                    # Extract numbers from text
                    number_match = re.search(r'[\d,]+\.?\d*', cell.replace('(', '').replace(')', ''))
                    if number_match:
                        try:
                            value = float(number_match.group().replace(',', ''))
                            # Handle negative numbers (in parentheses)
                            if '(' in cell and ')' in cell:
                                value = -value
                            numeric_row.append(value)
                        except ValueError:
                            numeric_row.append(0)
                    else:
                        numeric_row.append(0)
                else:
                    numeric_row.append(0)
            numeric_data.append(numeric_row)
        
        # Calculate column totals
        if numeric_data:
            num_cols = len(numeric_data[0])
            for col_idx in range(1, num_cols):  # Skip first column (usually labels)
                column_total = sum(row[col_idx] for row in numeric_data[1:])  # Skip header
                totals['calculated_totals'][f'column_{col_idx}'] = column_total
        
        return totals

# Global instance
financial_processor = FinancialTableProcessor()
