"""
User Feedback Integration System for Yark Tabular Extraction
Provides mechanisms for user feedback, manual corrections, and learning from errors
"""

import os
import json
import time
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from typing import List, Dict, Tuple, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import threading

@dataclass
class FeedbackEntry:
    """Individual feedback entry"""
    timestamp: str
    file_path: str
    feedback_type: str  # 'correction', 'quality_rating', 'error_report', 'suggestion'
    original_result: Any
    corrected_result: Any
    user_rating: Optional[int]  # 1-5 scale
    user_comments: str
    processing_metadata: Dict[str, Any]

class UserFeedbackCollector:
    """Collects and manages user feedback for continuous improvement"""
    
    def __init__(self, feedback_file: str = "user_feedback.json"):
        self.feedback_file = feedback_file
        self.feedback_entries = []
        self.load_existing_feedback()
        
        # Feedback categories
        self.feedback_types = {
            'correction': 'Manual correction of extracted data',
            'quality_rating': 'User rating of extraction quality',
            'error_report': 'Report of processing errors',
            'suggestion': 'User suggestions for improvement'
        }
        
        # Learning patterns
        self.learning_patterns = {
            'common_errors': {},
            'correction_patterns': {},
            'quality_trends': [],
            'user_preferences': {}
        }
    
    def load_existing_feedback(self):
        """Load existing feedback from file"""
        if os.path.exists(self.feedback_file):
            try:
                with open(self.feedback_file, 'r') as f:
                    data = json.load(f)
                    self.feedback_entries = [
                        FeedbackEntry(**entry) for entry in data.get('feedback_entries', [])
                    ]
                    self.learning_patterns = data.get('learning_patterns', self.learning_patterns)
                print(f"📚 Loaded {len(self.feedback_entries)} feedback entries")
            except Exception as e:
                print(f"⚠️ Could not load feedback file: {e}")
    
    def save_feedback(self):
        """Save feedback to file"""
        try:
            data = {
                'feedback_entries': [asdict(entry) for entry in self.feedback_entries],
                'learning_patterns': self.learning_patterns,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.feedback_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            print(f"💾 Saved {len(self.feedback_entries)} feedback entries")
        except Exception as e:
            print(f"❌ Could not save feedback: {e}")
    
    def collect_quality_rating(self, file_path: str, extracted_data: List[List[str]], 
                             processing_metadata: Dict[str, Any]) -> Optional[FeedbackEntry]:
        """Collect user quality rating for extraction results"""
        
        # Create feedback dialog
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        try:
            # Show extraction results for review
            feedback_dialog = QualityRatingDialog(root, file_path, extracted_data)
            root.wait_window(feedback_dialog.dialog)
            
            if feedback_dialog.result:
                feedback_entry = FeedbackEntry(
                    timestamp=datetime.now().isoformat(),
                    file_path=file_path,
                    feedback_type='quality_rating',
                    original_result=extracted_data,
                    corrected_result=feedback_dialog.result.get('corrected_data'),
                    user_rating=feedback_dialog.result.get('rating'),
                    user_comments=feedback_dialog.result.get('comments', ''),
                    processing_metadata=processing_metadata
                )
                
                self.feedback_entries.append(feedback_entry)
                self.update_learning_patterns(feedback_entry)
                self.save_feedback()
                
                return feedback_entry
        
        except Exception as e:
            print(f"⚠️ Error collecting feedback: {e}")
        
        finally:
            root.destroy()
        
        return None
    
    def collect_error_report(self, file_path: str, error_details: Dict[str, Any], 
                           processing_metadata: Dict[str, Any]) -> FeedbackEntry:
        """Collect error report from user"""
        
        feedback_entry = FeedbackEntry(
            timestamp=datetime.now().isoformat(),
            file_path=file_path,
            feedback_type='error_report',
            original_result=None,
            corrected_result=None,
            user_rating=None,
            user_comments=f"Error: {error_details.get('error_message', 'Unknown error')}",
            processing_metadata=processing_metadata
        )
        
        self.feedback_entries.append(feedback_entry)
        self.update_learning_patterns(feedback_entry)
        self.save_feedback()
        
        return feedback_entry
    
    def collect_manual_correction(self, file_path: str, original_data: List[List[str]], 
                                corrected_data: List[List[str]], 
                                processing_metadata: Dict[str, Any]) -> FeedbackEntry:
        """Collect manual corrections from user"""
        
        feedback_entry = FeedbackEntry(
            timestamp=datetime.now().isoformat(),
            file_path=file_path,
            feedback_type='correction',
            original_result=original_data,
            corrected_result=corrected_data,
            user_rating=None,
            user_comments="Manual correction provided",
            processing_metadata=processing_metadata
        )
        
        self.feedback_entries.append(feedback_entry)
        self.update_learning_patterns(feedback_entry)
        self.save_feedback()
        
        return feedback_entry
    
    def update_learning_patterns(self, feedback_entry: FeedbackEntry):
        """Update learning patterns based on feedback"""
        
        if feedback_entry.feedback_type == 'quality_rating':
            # Track quality trends
            self.learning_patterns['quality_trends'].append({
                'timestamp': feedback_entry.timestamp,
                'rating': feedback_entry.user_rating,
                'file_type': self.detect_file_type(feedback_entry.file_path),
                'processing_method': feedback_entry.processing_metadata.get('method', 'unknown')
            })
        
        elif feedback_entry.feedback_type == 'correction':
            # Analyze correction patterns
            if feedback_entry.original_result and feedback_entry.corrected_result:
                corrections = self.analyze_corrections(
                    feedback_entry.original_result, 
                    feedback_entry.corrected_result
                )
                
                for correction in corrections:
                    pattern_key = f"{correction['type']}_{correction['pattern']}"
                    if pattern_key not in self.learning_patterns['correction_patterns']:
                        self.learning_patterns['correction_patterns'][pattern_key] = 0
                    self.learning_patterns['correction_patterns'][pattern_key] += 1
        
        elif feedback_entry.feedback_type == 'error_report':
            # Track common errors
            error_type = feedback_entry.processing_metadata.get('error_type', 'unknown')
            if error_type not in self.learning_patterns['common_errors']:
                self.learning_patterns['common_errors'][error_type] = 0
            self.learning_patterns['common_errors'][error_type] += 1
    
    def detect_file_type(self, file_path: str) -> str:
        """Detect file type from path"""
        filename = os.path.basename(file_path).lower()
        
        if any(keyword in filename for keyword in ['balance', 'sheet', 'financial']):
            return 'financial'
        elif any(keyword in filename for keyword in ['math', 'equation', 'formula']):
            return 'mathematical'
        else:
            return 'general'
    
    def analyze_corrections(self, original: List[List[str]], 
                          corrected: List[List[str]]) -> List[Dict[str, Any]]:
        """Analyze corrections to identify patterns"""
        
        corrections = []
        
        # Compare cell by cell
        for i, (orig_row, corr_row) in enumerate(zip(original, corrected)):
            for j, (orig_cell, corr_cell) in enumerate(zip(orig_row, corr_row)):
                if orig_cell != corr_cell:
                    correction = {
                        'type': 'cell_correction',
                        'position': (i, j),
                        'original': orig_cell,
                        'corrected': corr_cell,
                        'pattern': self.identify_correction_pattern(orig_cell, corr_cell)
                    }
                    corrections.append(correction)
        
        return corrections
    
    def identify_correction_pattern(self, original: str, corrected: str) -> str:
        """Identify the type of correction pattern"""
        
        if not original and corrected:
            return 'missing_text_added'
        elif original and not corrected:
            return 'incorrect_text_removed'
        elif original.isdigit() and corrected.isdigit():
            return 'number_correction'
        elif '$' in original or '$' in corrected:
            return 'currency_correction'
        elif len(original) == 1 and len(corrected) == 1:
            return 'character_substitution'
        elif original.lower() != corrected.lower():
            return 'case_correction'
        else:
            return 'text_correction'
    
    def get_improvement_suggestions(self) -> List[Dict[str, Any]]:
        """Generate improvement suggestions based on feedback patterns"""
        
        suggestions = []
        
        # Analyze quality trends
        if self.learning_patterns['quality_trends']:
            recent_ratings = [
                entry['rating'] for entry in self.learning_patterns['quality_trends'][-10:]
                if entry['rating'] is not None
            ]
            
            if recent_ratings:
                avg_rating = sum(recent_ratings) / len(recent_ratings)
                
                if avg_rating < 3.0:
                    suggestions.append({
                        'type': 'quality_improvement',
                        'priority': 'high',
                        'suggestion': 'Recent quality ratings are low. Consider improving OCR accuracy or preprocessing.',
                        'data': {'avg_rating': avg_rating, 'sample_size': len(recent_ratings)}
                    })
        
        # Analyze common errors
        if self.learning_patterns['common_errors']:
            most_common_error = max(
                self.learning_patterns['common_errors'].items(),
                key=lambda x: x[1]
            )
            
            if most_common_error[1] > 5:  # More than 5 occurrences
                suggestions.append({
                    'type': 'error_reduction',
                    'priority': 'medium',
                    'suggestion': f'Focus on reducing {most_common_error[0]} errors (occurred {most_common_error[1]} times)',
                    'data': {'error_type': most_common_error[0], 'count': most_common_error[1]}
                })
        
        # Analyze correction patterns
        if self.learning_patterns['correction_patterns']:
            most_common_correction = max(
                self.learning_patterns['correction_patterns'].items(),
                key=lambda x: x[1]
            )
            
            if most_common_correction[1] > 3:  # More than 3 occurrences
                suggestions.append({
                    'type': 'correction_pattern',
                    'priority': 'medium',
                    'suggestion': f'Implement automatic correction for {most_common_correction[0]} pattern',
                    'data': {'pattern': most_common_correction[0], 'count': most_common_correction[1]}
                })
        
        return suggestions
    
    def generate_feedback_report(self) -> Dict[str, Any]:
        """Generate comprehensive feedback report"""
        
        total_feedback = len(self.feedback_entries)
        
        # Count by type
        feedback_by_type = {}
        for entry in self.feedback_entries:
            feedback_by_type[entry.feedback_type] = feedback_by_type.get(entry.feedback_type, 0) + 1
        
        # Calculate average rating
        ratings = [
            entry.user_rating for entry in self.feedback_entries 
            if entry.user_rating is not None
        ]
        avg_rating = sum(ratings) / len(ratings) if ratings else None
        
        # Get recent feedback
        recent_feedback = sorted(
            self.feedback_entries, 
            key=lambda x: x.timestamp, 
            reverse=True
        )[:10]
        
        report = {
            'summary': {
                'total_feedback_entries': total_feedback,
                'feedback_by_type': feedback_by_type,
                'average_rating': avg_rating,
                'rating_count': len(ratings)
            },
            'learning_patterns': self.learning_patterns,
            'improvement_suggestions': self.get_improvement_suggestions(),
            'recent_feedback': [asdict(entry) for entry in recent_feedback]
        }
        
        return report

class QualityRatingDialog:
    """Dialog for collecting quality ratings and corrections"""
    
    def __init__(self, parent, file_path: str, extracted_data: List[List[str]]):
        self.parent = parent
        self.file_path = file_path
        self.extracted_data = extracted_data
        self.result = None
        
        self.create_dialog()
    
    def create_dialog(self):
        """Create the feedback dialog"""
        
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Quality Feedback")
        self.dialog.geometry("800x600")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # File info
        info_frame = ttk.Frame(self.dialog)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(info_frame, text=f"File: {os.path.basename(self.file_path)}", 
                 font=('Arial', 12, 'bold')).pack(anchor='w')
        
        # Extracted data display
        data_frame = ttk.LabelFrame(self.dialog, text="Extracted Table Data")
        data_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Create treeview for table display
        columns = [f"Col {i+1}" for i in range(max(len(row) for row in self.extracted_data) if self.extracted_data else 1)]
        
        self.tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=100)
        
        # Add data
        for row in self.extracted_data:
            # Pad row to match column count
            padded_row = row + [''] * (len(columns) - len(row))
            self.tree.insert('', 'end', values=padded_row)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(data_frame, orient='vertical', command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(data_frame, orient='horizontal', command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        data_frame.grid_rowconfigure(0, weight=1)
        data_frame.grid_columnconfigure(0, weight=1)
        
        # Rating frame
        rating_frame = ttk.LabelFrame(self.dialog, text="Quality Rating")
        rating_frame.pack(fill='x', padx=10, pady=5)
        
        ttk.Label(rating_frame, text="Rate the extraction quality (1=Poor, 5=Excellent):").pack(anchor='w')
        
        self.rating_var = tk.IntVar(value=3)
        rating_buttons_frame = ttk.Frame(rating_frame)
        rating_buttons_frame.pack(anchor='w', pady=5)
        
        for i in range(1, 6):
            ttk.Radiobutton(rating_buttons_frame, text=str(i), variable=self.rating_var, 
                           value=i).pack(side='left', padx=5)
        
        # Comments frame
        comments_frame = ttk.LabelFrame(self.dialog, text="Comments (Optional)")
        comments_frame.pack(fill='x', padx=10, pady=5)
        
        self.comments_text = tk.Text(comments_frame, height=3)
        self.comments_text.pack(fill='x', padx=5, pady=5)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Button(button_frame, text="Submit Feedback", 
                  command=self.submit_feedback).pack(side='right', padx=5)
        ttk.Button(button_frame, text="Skip", 
                  command=self.skip_feedback).pack(side='right', padx=5)
    
    def submit_feedback(self):
        """Submit the feedback"""
        
        self.result = {
            'rating': self.rating_var.get(),
            'comments': self.comments_text.get('1.0', 'end-1c'),
            'corrected_data': None  # Could be extended to allow corrections
        }
        
        self.dialog.destroy()
    
    def skip_feedback(self):
        """Skip feedback collection"""
        
        self.result = None
        self.dialog.destroy()

# Global instance
feedback_collector = UserFeedbackCollector()
