@echo off
echo ========================================
echo Tesseract OCR Installation for Windows
echo ========================================
echo.

echo Checking if Tesseract is already installed...
tesseract --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Tesseract is already installed and available in PATH
    tesseract --version
    echo.
    echo Installation complete!
    pause
    exit /b 0
)

echo ❌ Tesseract not found in PATH
echo.

echo Checking if winget is available...
winget --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Winget is available, installing Tesseract...
    echo.
    winget install UB-Mannheim.TesseractOCR
    
    echo.
    echo Adding Tesseract to PATH...
    set "TESSERACT_PATH=C:\Program Files\Tesseract-OCR"
    if exist "%TESSERACT_PATH%" (
        echo ✅ Tesseract installed successfully
        echo.
        echo Please restart your command prompt or IDE to use Tesseract
        echo Or manually add this path to your system PATH: %TESSERACT_PATH%
    ) else (
        echo ⚠️  Tesseract installation may have failed or installed to a different location
        echo Please check the installation manually
    )
) else (
    echo ❌ Winget not available
    echo.
    echo Please install Tesseract manually:
    echo 1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
    echo 2. Install the executable
    echo 3. Add the installation directory to your system PATH
    echo.
    echo Common installation paths:
    echo - C:\Program Files\Tesseract-OCR
    echo - C:\Program Files (x86)\Tesseract-OCR
)

echo.
echo ========================================
echo Installation process completed
echo ========================================
pause
